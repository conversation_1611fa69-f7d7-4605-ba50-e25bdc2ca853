# CAPTCHA Solver for Mobile App

This project contains scripts to automatically solve CAPTCHAs in a mobile app using ADB (Android Debug Bridge), OpenCV, and Tesseract OCR.

## Prerequisites

- Python 3.6+
- ADB (Android Debug Bridge) installed and configured
- Tesseract OCR installed (https://github.com/UB-Mannheim/tesseract/wiki)
- Connected Android device with USB debugging enabled

## Required Python Packages

```
pip install opencv-python numpy pytesseract pure-python-adb requests
```

## Scripts Overview

### 1. `captcha_solver_final.py`

The main script that:
- Takes a screenshot of the device
- Crops the CAPTCHA text area
- Processes the image for optimal OCR
- Extracts the text using Tesseract OCR
- Inputs the text and submits it
- Repeats the process

### 2. `test_captcha_crop.py`

A testing script to verify the crop area for the CAPTCHA text.

### 3. `test_clean_captcha.py`

A script to test OCR directly on a clean CAPTCHA image.

### 4. `save_clean_captcha.py`

A utility script to save and test O<PERSON> on a clean CAPTCHA image.

## Usage

1. Connect your Android device via USB and enable USB debugging
2. Make sure ADB is properly set up and can detect your device
3. Run the main script:

```
python captcha_solver_final.py
```

## Customization

You may need to adjust the following parameters in the scripts:

- Crop coordinates for the CAPTCHA text area
- Input field and submit button coordinates
- OCR configuration parameters

## Troubleshooting

If the OCR is not accurately recognizing the CAPTCHA text:

1. Check the saved images in the working directory to see if the crop area is correct
2. Adjust the crop coordinates in the script
3. Try different preprocessing methods (grayscale, binary threshold, etc.)
4. Adjust the Tesseract OCR configuration parameters

## Notes

- The script is designed to work with the specific CAPTCHA format shown in the screenshots
- It may need adjustments for different devices or screen resolutions
- Press Ctrl+C to stop the script at any time
