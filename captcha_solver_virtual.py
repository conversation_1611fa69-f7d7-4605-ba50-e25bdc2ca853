import sys
import time
import logging
import os
import signal
import subprocess
from datetime import datetime

# Import the original CAPTCHA solver code
# We'll use import * to get all the functions and variables
from captcha_solver_rapid import *

# Import the virtual display module
from virtual_display import start_virtual_display, stop_virtual_display

# Set up logging
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, 'captcha_solver_virtual.log')

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def log_and_print(message, level="info"):
    """Log a message and print it to console"""
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "warning":
        logging.warning(message)

    # Also print to console
    print(message)

def main_virtual():
    """Run the CAPTCHA solver with virtual display settings"""
    log_and_print("Starting CAPTCHA Solver with Virtual Display Settings")
    log_and_print(f"Log file: {log_file}")

    # Set up virtual display settings
    virtual_display = start_virtual_display()

    if not virtual_display:
        log_and_print("Failed to set up virtual display settings.", "error")
        return

    try:
        # Connect to the device
        log_and_print("Connecting to device...")
        device = connect_to_device()
        if not device:
            log_and_print("Failed to connect to device", "error")
            return

        # Run the CAPTCHA solver
        solved_count = 0
        start_time = datetime.now()

        while True:
            try:
                # Calculate and log statistics
                current_time = datetime.now()
                elapsed_time = (current_time - start_time).total_seconds() / 60  # minutes

                if elapsed_time > 0:
                    rate = solved_count / elapsed_time  # CAPTCHAs per minute
                else:
                    rate = 0

                log_and_print(f"Running for {elapsed_time:.1f} minutes. Solving rate: {rate:.2f} CAPTCHAs/minute")

                # Open the app
                log_and_print("\n--- New CAPTCHA Attempt ---")
                open_captcha_work()

                # Capture screenshot
                log_and_print("Capturing screenshot...")
                screenshot_path = capture_screenshot(device)

                if not screenshot_path:
                    log_and_print("Failed to capture screenshot. Retrying...", "error")
                    close_captcha_work()
                    continue

                # Extract CAPTCHA text
                log_and_print("Processing CAPTCHA...")
                captcha_text = extract_captcha_text(screenshot_path)

                if captcha_text and len(captcha_text) >= 5:
                    log_and_print(f"Final CAPTCHA text: '{captcha_text}'")

                    # Input text and submit
                    if input_and_submit(captcha_text):
                        solved_count += 1
                        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
                    else:
                        log_and_print("Failed to input and submit", "error")
                else:
                    log_and_print("Failed to detect valid CAPTCHA text", "error")

                # Close the app after solving
                close_captcha_work()

            except Exception as e:
                log_and_print(f"Error in main loop: {e}", "error")
                # Try to recover
                try:
                    close_captcha_work()
                except:
                    pass
                log_and_print("Waiting 30 seconds before retrying...", "warning")
                time.sleep(30)

    except KeyboardInterrupt:
        log_and_print("Process stopped by user")
        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        log_and_print(f"Error in main process: {e}", "error")
    finally:
        # Clean up virtual display settings
        stop_virtual_display(virtual_display)
        log_and_print("Virtual display settings cleaned up")

if __name__ == "__main__":
    main_virtual()
