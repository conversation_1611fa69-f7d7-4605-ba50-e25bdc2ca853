@echo off
echo Installing scrcpy for virtual display...
echo =====================================
echo.

set SCRCPY_VERSION=2.1.1
set DOWNLOAD_DIR=%TEMP%\scrcpy_download
set INSTALL_DIR=%LOCALAPPDATA%\scrcpy

echo Creating directories...
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"
if exist "%INSTALL_DIR%" rmdir /S /Q "%INSTALL_DIR%"
mkdir "%INSTALL_DIR%"

echo Downloading scrcpy v%SCRCPY_VERSION%...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/Genymobile/scrcpy/releases/download/v%SCRCPY_VERSION%/scrcpy-win64-v%SCRCPY_VERSION%.zip' -OutFile '%DOWNLOAD_DIR%\scrcpy.zip'}"

echo Extracting files...
powershell -Command "& {Expand-Archive -Path '%DOWNLOAD_DIR%\scrcpy.zip' -DestinationPath '%DOWNLOAD_DIR%\temp' -Force}"

echo Moving files to installation directory...
for /d %%d in ("%DOWNLOAD_DIR%\temp\*") do (
    xcopy "%%d\*" "%INSTALL_DIR%" /E /I /Y
)

echo Adding to PATH...
setx PATH "%PATH%;%INSTALL_DIR%"

echo Verifying installation...
if exist "%INSTALL_DIR%\scrcpy.exe" (
    echo scrcpy.exe found at %INSTALL_DIR%\scrcpy.exe
) else (
    echo ERROR: scrcpy.exe not found in %INSTALL_DIR%
    echo Contents of %INSTALL_DIR%:
    dir "%INSTALL_DIR%"
    echo.
    echo Contents of %DOWNLOAD_DIR%\temp:
    dir "%DOWNLOAD_DIR%\temp"
)

echo Cleaning up...
rmdir /S /Q "%DOWNLOAD_DIR%"

echo.
echo Installation complete!
echo scrcpy has been installed to: %INSTALL_DIR%
echo.
echo You may need to restart your command prompt for the PATH changes to take effect.
echo.
pause
