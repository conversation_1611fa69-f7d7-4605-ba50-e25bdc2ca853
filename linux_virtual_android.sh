#!/bin/bash

# Linux Virtual Android Environment Setup Script
# This script sets up a headless Android environment in Linux

echo "Setting up Virtual Android Environment in Linux"
echo "==============================================="

# Check for required tools
command -v apt-get >/dev/null 2>&1 || { echo "This script requires apt-get. Please use a Debian/Ubuntu based system."; exit 1; }

# Install required packages
echo "Installing required packages..."
sudo apt-get update
sudo apt-get install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils android-tools-adb python3 python3-pip

# Install Android emulator
echo "Installing Android emulator..."
sudo apt-get install -y android-sdk

# Set up environment variables
export ANDROID_SDK_ROOT=/usr/lib/android-sdk
export PATH=$PATH:$ANDROID_SDK_ROOT/tools/bin:$ANDROID_SDK_ROOT/platform-tools

# Install required Python packages
echo "Installing Python packages..."
pip3 install opencv-python easyocr numpy pillow

# Create a virtual Android device
echo "Creating virtual Android device..."
avdmanager create avd -n CaptchaDevice -k "system-images;android-30;google_apis;x86_64" -d "pixel"

# Create a script to run the emulator headlessly
cat > run_headless_emulator.sh << 'EOL'
#!/bin/bash

# Run the Android emulator headlessly
echo "Starting headless Android emulator..."
emulator -avd CaptchaDevice -no-window -no-audio -no-boot-anim &
EMULATOR_PID=$!

# Wait for the emulator to boot
echo "Waiting for emulator to boot..."
adb wait-for-device

# Install the CAPTCHA Work app
echo "Installing CAPTCHA Work app..."
# You'll need to provide the APK file for the CAPTCHA Work app
# adb install captcha_work.apk

# Run the CAPTCHA solver script
echo "Running CAPTCHA solver..."
python3 linux_captcha_solver.py

# Clean up
kill $EMULATOR_PID
EOL

chmod +x run_headless_emulator.sh

echo "Setup complete!"
echo "To run the headless Android emulator, execute: ./run_headless_emulator.sh"
