#!/data/data/com.termux/files/usr/bin/bash

# CAPTCHA Solver Launcher
echo "CAPTCHA Solver Launcher"
echo "======================="
echo

# Change to home directory
cd $HOME

# Create bin directory if it doesn't exist
mkdir -p ~/bin

# Install required packages
echo "Installing required packages..."
pkg update -y
pkg install -y python tesseract

# Install Python packages
echo "Installing Python packages..."
pip install pillow numpy pytesseract

# Copy the CAPTCHA solver script
echo "Setting up CAPTCHA solver script..."
cp /sdcard/Download/captcha_solver_pil.py ~/bin/
chmod +x ~/bin/captcha_solver_pil.py

# Run the CAPTCHA solver
echo "Starting CAPTCHA solver..."
python ~/bin/captcha_solver_pil.py

# Keep terminal open after script finishes
echo "Press Enter to close"
read
