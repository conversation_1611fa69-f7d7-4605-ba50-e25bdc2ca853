import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import time
import os
import subprocess
import logging
import traceback

# Import our virtual CAPTCHA solver
from captcha_solver_virtual import main_virtual

class CaptchaSolverService(win32serviceutil.ServiceFramework):
    _svc_name_ = "CaptchaSolverVirtual"
    _svc_display_name_ = "CAPTCHA Solver Virtual Service"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_running = True
        
        # Set up logging
        log_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(log_dir, 'captcha_service.log')
        
        logging.basicConfig(
            filename=log_file,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        logging.info(f"Service initialized. Log file: {log_file}")
    
    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        logging.info("Service stop requested")
    
    def SvcDoRun(self):
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        logging.info("Service starting")
        self.main()
    
    def main(self):
        try:
            # Run the virtual CAPTCHA solver
            logging.info("Starting virtual CAPTCHA solver")
            main_virtual()
        except Exception as e:
            logging.error(f"Service error: {e}")
            logging.error(traceback.format_exc())

# For testing the service without installing
def run_service_interactively():
    print("Running CAPTCHA Solver service interactively (for testing)")
    print("Press Ctrl+C to stop")
    try:
        main_virtual()
    except KeyboardInterrupt:
        print("Service stopped")

if __name__ == '__main__':
    if len(sys.argv) == 1:
        try:
            # If run without arguments, try to start as a service
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(CaptchaSolverService)
            servicemanager.StartServiceCtrlDispatcher()
        except Exception as e:
            # If that fails, run interactively
            print(f"Could not start as a service: {e}")
            print("Running interactively instead")
            run_service_interactively()
    elif sys.argv[1].lower() == 'test':
        # Run interactively for testing
        run_service_interactively()
    else:
        # Handle service commands (install, remove, start, stop, etc.)
        win32serviceutil.HandleCommandLine(CaptchaSolverService)
