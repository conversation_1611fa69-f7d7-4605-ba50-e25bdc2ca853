#!/usr/bin/env python3

import subprocess
import time
import os
import logging
from datetime import datetime
import cv2
import numpy as np
import pytesseract

# Set up logging
log_dir = os.path.expanduser("~/storage/downloads")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, "captcha_solver.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Configuration
CAPTCHA_APP_PACKAGE = "com.aadevelopers.captchaapp"
CAPTCHA_APP_ACTIVITY = "com.aadevelopers.captchaapp.views.SplashActivity"

# Coordinates for input field and submit button
INPUT_FIELD_X = 620
INPUT_FIELD_Y = 1500
SUBMIT_BUTTON_X = 598
SUBMIT_BUTTON_Y = 1732

# Timing settings
DELAY_AFTER_TAP = 0.5
DELAY_AFTER_TEXT = 0.5
DELAY_AFTER_SUBMIT = 0.5
DELAY_AFTER_CLOSE = 0.5
DELAY_AFTER_OPEN = 2.0
DELAY_BEFORE_SCREENSHOT = 1.5
DELAY_AFTER_SCREENSHOT = 1.0

def log_and_print(message, level="info"):
    """Log a message and print it to console"""
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "warning":
        logging.warning(message)
    
    # Also print to console
    print(message)

def run_adb_command(command):
    """Run an ADB command"""
    try:
        full_command = f"adb {command}"
        result = subprocess.run(
            full_command,
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            log_and_print(f"ADB command error: {result.stderr}", "error")
            return False
            
        return True
    except Exception as e:
        log_and_print(f"Error running ADB command: {e}", "error")
        return False

def setup_wireless_adb():
    """Set up wireless ADB connection"""
    try:
        log_and_print("Setting up wireless ADB...")
        
        # Check if ADB server is running
        result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
        
        if "List of devices" not in result.stdout:
            log_and_print("Starting ADB server...")
            subprocess.run("adb start-server", shell=True, check=True)
        
        # Check if we're already connected
        if "device" in result.stdout:
            log_and_print("Already connected to a device")
            return True
            
        # Try to connect to localhost
        log_and_print("Trying to connect to device...")
        subprocess.run("adb connect localhost:5555", shell=True, check=True)
        
        # Verify connection
        devices_result = subprocess.run(
            "adb devices", 
            shell=True,
            capture_output=True, 
            text=True
        )
        
        if "localhost:5555" in devices_result.stdout and "device" in devices_result.stdout:
            log_and_print("Wireless ADB connection established successfully")
            return True
        else:
            log_and_print("Failed to establish wireless connection", "error")
            log_and_print("Please make sure Wireless Debugging is enabled", "error")
            return False
               
    except Exception as e:
        log_and_print(f"Error setting up wireless ADB: {e}", "error")
        return False

def open_captcha_app():
    """Open the CAPTCHA app"""
    try:
        log_and_print("Opening CAPTCHA app...")
        
        # Launch the app
        run_adb_command(f"shell am start -n {CAPTCHA_APP_PACKAGE}/{CAPTCHA_APP_ACTIVITY}")
        
        # Wait for the app to open
        log_and_print(f"Waiting {DELAY_AFTER_OPEN}s for app to open properly...")
        time.sleep(DELAY_AFTER_OPEN)
        
        log_and_print("CAPTCHA app opened")
        return True
    
    except Exception as e:
        log_and_print(f"Error opening CAPTCHA app: {e}", "error")
        return False

def close_captcha_app():
    """Close the CAPTCHA app"""
    try:
        log_and_print("Closing CAPTCHA app...")
        
        # Force stop the app
        run_adb_command(f"shell am force-stop {CAPTCHA_APP_PACKAGE}")
        
        # Wait for the app to close
        log_and_print(f"Waiting {DELAY_AFTER_CLOSE}s for app to close...")
        time.sleep(DELAY_AFTER_CLOSE)
        
        log_and_print("CAPTCHA app closed")
        return True
    
    except Exception as e:
        log_and_print(f"Error closing CAPTCHA app: {e}", "error")
        return False

def capture_screenshot(local_path=None):
    """Capture a screenshot"""
    if local_path is None:
        local_path = os.path.expanduser("~/storage/downloads/screen.png")
        
    try:
        # Wait additional time before taking screenshot
        log_and_print(f"Waiting {DELAY_BEFORE_SCREENSHOT}s before taking screenshot...")
        time.sleep(DELAY_BEFORE_SCREENSHOT)
        
        log_and_print("Capturing screenshot...")
        
        # Use ADB to take a screenshot
        run_adb_command(f"exec-out screencap -p > {local_path}")
        
        log_and_print(f"Screenshot saved to {local_path}")
        
        # Add delay after screenshot
        log_and_print(f"Waiting {DELAY_AFTER_SCREENSHOT}s after screenshot...")
        time.sleep(DELAY_AFTER_SCREENSHOT)
        
        return local_path
    except Exception as e:
        log_and_print(f"Error capturing screenshot: {e}", "error")
        return None

def extract_captcha_text(image_path):
    """Extract CAPTCHA text from the image using Tesseract OCR"""
    # Load the image
    img = cv2.imread(image_path)

    if img is None:
        log_and_print(f"Error: Could not load image from {image_path}", "error")
        return ""

    # Get image dimensions
    height, width, _ = img.shape
    log_and_print(f"Image dimensions: {width}x{height}")

    # EXACT DIMENSIONS APPROACH: Use the exact dimensions from the screenshot (606 x 336)
    # Calculate the center position to place our crop
    center_x = width // 2
    center_y = height // 3  # Position in the upper third of the screen
    
    # Use exact dimensions: 606 x 336
    crop_width = 606
    crop_height = 336
    
    # Calculate the top-left corner of our crop area
    crop_x = center_x - (crop_width // 2)
    crop_y = center_y - (crop_height // 2)
    
    # Make sure we don't go out of bounds
    crop_x = max(0, min(crop_x, width - crop_width))
    crop_y = max(0, min(crop_y, height - crop_height))
    
    log_and_print(f"Exact crop: x={crop_x}, y={crop_y}, width={crop_width}, height={crop_height}")
    
    # Crop using the exact dimensions
    captcha_area = img[crop_y:crop_y+crop_height, crop_x:crop_x+crop_width]
    cv2.imwrite(os.path.expanduser("~/storage/downloads/captcha_area.png"), captcha_area)
    
    # Use this as our main crop for text detection
    center_crop = captcha_area

    # Preprocess the image for better OCR
    gray = cv2.cvtColor(center_crop, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)
    
    # Save the preprocessed image
    cv2.imwrite(os.path.expanduser("~/storage/downloads/captcha_preprocessed.png"), binary)
    
    # Use Tesseract OCR to detect text
    log_and_print("Running OCR on the cropped image...")
    text = pytesseract.image_to_string(binary, config='--psm 7')
    
    # Clean the text (keep only alphanumeric characters)
    cleaned_text = ''.join(c for c in text if c.isalnum())
    
    log_and_print(f"Detected text: '{cleaned_text}'")
    
    # If we found text with at least 5 characters and at most 15, return it
    if cleaned_text and 5 <= len(cleaned_text) <= 15:
        return cleaned_text
    
    # If Tesseract didn't find any text, try a different approach
    # Convert to grayscale and invert colors (white text on black background)
    log_and_print("Running OCR on the inverted image...")
    inverted = cv2.bitwise_not(gray)
    cv2.imwrite(os.path.expanduser("~/storage/downloads/inverted.png"), inverted)
    
    # Try OCR on the inverted image
    text = pytesseract.image_to_string(inverted, config='--psm 7')
    
    # Clean the text
    cleaned_text = ''.join(c for c in text if c.isalnum())
    
    log_and_print(f"Detected text (inverted): '{cleaned_text}'")
    
    if cleaned_text and 5 <= len(cleaned_text) <= 15:
        return cleaned_text
    
    # If still no text found, try the full image as a last resort
    log_and_print("Trying alternative approach...")
    log_and_print("Running OCR on the full image...")
    text = pytesseract.image_to_string(img, config='--psm 11')
    
    # Clean the text
    cleaned_text = ''.join(c for c in text if c.isalnum())
    
    log_and_print(f"Detected text (full image): '{cleaned_text}'")
    
    # Extract potential CAPTCHA text (5-15 alphanumeric characters)
    import re
    potential_captchas = re.findall(r'[A-Za-z0-9]{5,15}', cleaned_text)
    
    if potential_captchas:
        best_text = potential_captchas[0]
        log_and_print(f"Possible CAPTCHA text: '{best_text}'")
        return best_text
    
    return ""

def input_and_submit(text):
    """Input the CAPTCHA text and submit it"""
    log_and_print(f"Tapping input field at ({INPUT_FIELD_X}, {INPUT_FIELD_Y})...")
    try:
        # Tap on the input field
        run_adb_command(f"shell input tap {INPUT_FIELD_X} {INPUT_FIELD_Y}")
        
        # Add delay after tap
        log_and_print(f"Waiting {DELAY_AFTER_TAP}s after tap...")
        time.sleep(DELAY_AFTER_TAP)
        
        # Clear any existing text
        run_adb_command("shell input keyevent KEYCODE_CTRL_A")
        run_adb_command("shell input keyevent KEYCODE_DEL")
        
        # Add delay after clearing text
        time.sleep(DELAY_AFTER_TAP)
        
        log_and_print(f"Entering text: '{text}'...")
        # Input the text - strip any whitespace and ensure only alphanumeric characters
        cleaned_text = ''.join(c for c in text if c.isalnum())
        log_and_print(f"Cleaned text for input: '{cleaned_text}'")
        
        run_adb_command(f"shell input text {cleaned_text}")
        
        # Add delay after entering text
        log_and_print(f"Waiting {DELAY_AFTER_TEXT}s after entering text...")
        time.sleep(DELAY_AFTER_TEXT)
        
        log_and_print(f"Tapping submit button at ({SUBMIT_BUTTON_X}, {SUBMIT_BUTTON_Y})...")
        # Tap the submit button
        run_adb_command(f"shell input tap {SUBMIT_BUTTON_X} {SUBMIT_BUTTON_Y}")
        
        # Add delay after submission
        log_and_print(f"Waiting {DELAY_AFTER_SUBMIT}s after submission...")
        time.sleep(DELAY_AFTER_SUBMIT)
        return True
    except Exception as e:
        log_and_print(f"Error during input and submit: {e}", "error")
        return False

def main():
    """Main function to run the CAPTCHA solver"""
    log_and_print("Starting CAPTCHA Solver")
    
    # Set up wireless ADB
    if not setup_wireless_adb():
        log_and_print("Failed to set up wireless ADB. Exiting.", "error")
        return
    
    try:
        solved_count = 0
        last_texts = []  # Track the last 3 texts to detect repetition
        start_time = datetime.now()
        
        while True:
            try:
                # Calculate and log statistics
                current_time = datetime.now()
                elapsed_time = (current_time - start_time).total_seconds() / 60  # minutes
                
                if elapsed_time > 0:
                    rate = solved_count / elapsed_time  # CAPTCHAs per minute
                else:
                    rate = 0
                
                log_and_print(f"Running for {elapsed_time:.1f} minutes. Solving rate: {rate:.2f} CAPTCHAs/minute")
                
                # Open the app
                log_and_print("\n--- New CAPTCHA Attempt ---")
                open_captcha_app()
                
                # Capture screenshot
                screenshot_path = capture_screenshot()
                
                if not screenshot_path:
                    log_and_print("Failed to capture screenshot. Retrying...", "error")
                    close_captcha_app()
                    continue
                
                # Extract CAPTCHA text
                log_and_print("Processing CAPTCHA...")
                captcha_text = extract_captcha_text(screenshot_path)
                
                if captcha_text and len(captcha_text) >= 5:
                    log_and_print(f"Final CAPTCHA text: '{captcha_text}'")
                    
                    # Track the last 3 texts
                    last_texts.append(captcha_text)
                    if len(last_texts) > 3:
                        last_texts.pop(0)
                    
                    # Check if we've seen the same text 3 times in a row
                    if len(last_texts) == 3 and all(text == last_texts[0] for text in last_texts):
                        log_and_print("WARNING: Same CAPTCHA text detected 3 times in a row!", "warning")
                        log_and_print("This might indicate a problem with the app or the CAPTCHA system.", "warning")
                    
                    # Input text and submit
                    if input_and_submit(captcha_text):
                        solved_count += 1
                        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
                    else:
                        log_and_print("Failed to input and submit", "error")
                else:
                    log_and_print("Failed to detect valid CAPTCHA text", "error")
                
                # Close the app after solving
                close_captcha_app()
                
            except Exception as e:
                log_and_print(f"Error in main loop: {e}", "error")
                # Try to recover
                try:
                    close_captcha_app()
                except:
                    pass
                log_and_print("Waiting 30 seconds before retrying...", "warning")
                time.sleep(30)
        
    except KeyboardInterrupt:
        log_and_print("Process stopped by user")
        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        log_and_print(f"Error in main process: {e}", "error")

if __name__ == "__main__":
    main()
