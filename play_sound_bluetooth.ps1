# PowerShell script to play a sound through Bluetooth

# Check if the OnePlus device is connected as an audio device
$audioDevices = Get-CimInstance -Namespace "ROOT\cimv2" -ClassName Win32_SoundDevice | Where-Object { $_.Name -like "*OnePlus*" }

if ($audioDevices) {
    Write-Host "OnePlus device found as audio output. You can play sounds through it."
    
    # Try to set it as the default audio device
    Write-Host "Attempting to set OnePlus as default audio device..."
    
    # This requires the AudioDeviceCmdlets module which might not be installed
    # We'll provide instructions instead
    
    Write-Host "Please follow these steps to play sound on your phone:"
    Write-Host "1. Right-click on the sound icon in your taskbar"
    Write-Host "2. Select 'Open Sound settings'"
    Write-Host "3. Under 'Choose your output device', select your OnePlus 11R"
    Write-Host "4. Open any media player (YouTube, Windows Media Player, etc.)"
    Write-Host "5. Play audio at maximum volume"
    
    # Offer to open sound settings
    $openSettings = Read-Host "Would you like to open Sound Settings now? (Y/N)"
    if ($openSettings -eq "Y" -or $openSettings -eq "y") {
        Start-Process "ms-settings:sound"
    }
    
    # Offer to open YouTube with a loud sound
    $openYouTube = Read-Host "Would you like to open YouTube with a loud sound? (Y/N)"
    if ($openYouTube -eq "Y" -or $openYouTube -eq "y") {
        Start-Process "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    }
} else {
    Write-Host "OnePlus device not found as an audio output device."
    Write-Host "Please make sure your phone is connected via Bluetooth and paired as an audio device."
    
    # Check if the device is at least paired via Bluetooth
    $btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }
    
    if ($btDevices) {
        Write-Host "OnePlus device is paired via Bluetooth but not set up as an audio device."
        Write-Host "Please check your phone's Bluetooth settings to enable audio streaming."
    } else {
        Write-Host "OnePlus device not found in paired Bluetooth devices."
    }
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
