@echo off
echo Attempting to use PIN 1296 to unlock and control your OnePlus 11R...
echo.

echo Step 1: Checking if your phone is connected via ADB...
platform-tools\adb devices

echo Attempting to connect to your phone via ADB...
platform-tools\adb connect 192.168.0.249:5555
timeout /t 2 /nobreak >nul

echo Trying to unlock your phone with PIN 1296...
platform-tools\adb shell input keyevent 26
timeout /t 1 /nobreak >nul
platform-tools\adb shell input keyevent 82
timeout /t 1 /nobreak >nul
platform-tools\adb shell input text 1296
platform-tools\adb shell input keyevent 66
echo Phone unlock attempt complete.

echo.
echo Step 2: Attempting to play loud music on your unlocked phone...
echo.

echo Launching Apple Music...
platform-tools\adb shell am start -n com.apple.android.music/com.apple.android.music.onboarding.activities.SplashActivity
timeout /t 3 /nobreak >nul

echo Setting volume to maximum...
platform-tools\adb shell input keyevent 24 24 24 24 24 24 24 24 24 24 24 24 24 24 24
timeout /t 1 /nobreak >nul

echo Sending play command...
platform-tools\adb shell input keyevent 85
timeout /t 2 /nobreak >nul

echo.
echo If the above methods didn't work, we need to try alternative approaches.
echo.

echo Press any key to continue...
pause >nul
