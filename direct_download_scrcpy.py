import os
import sys
import requests
import zipfile
import io
import subprocess
import time
import shutil

def download_scrcpy():
    """Download scrcpy directly and extract it to a temporary directory"""
    print("Downloading scrcpy directly...")
    
    # Create a temporary directory
    temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "scrcpy_temp")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    
    # Download scrcpy
    url = "https://github.com/Genymobile/scrcpy/releases/download/v2.1.1/scrcpy-win64-v2.1.1.zip"
    print(f"Downloading from {url}...")
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        # Extract the zip file
        print("Extracting zip file...")
        z = zipfile.ZipFile(io.BytesIO(response.content))
        z.extractall(temp_dir)
        
        # Find the scrcpy.exe file
        for root, dirs, files in os.walk(temp_dir):
            if "scrcpy.exe" in files:
                scrcpy_path = os.path.join(root, "scrcpy.exe")
                print(f"Found scrcpy.exe at: {scrcpy_path}")
                return scrcpy_path
        
        print("ERROR: Could not find scrcpy.exe in the extracted files")
        return None
        
    except Exception as e:
        print(f"Error downloading scrcpy: {e}")
        return None

def run_virtual_display_with_scrcpy(scrcpy_path):
    """Run the virtual display with the downloaded scrcpy"""
    print(f"Running virtual display with scrcpy at {scrcpy_path}...")
    
    try:
        # Set the environment variable for the virtual_display.py script
        os.environ["SCRCPY_PATH"] = scrcpy_path
        
        # Import and run the virtual CAPTCHA solver
        print("Starting CAPTCHA solver...")
        from captcha_solver_virtual import main_virtual
        main_virtual()
        
    except Exception as e:
        print(f"Error running virtual display: {e}")

if __name__ == "__main__":
    # Download scrcpy
    scrcpy_path = download_scrcpy()
    
    if scrcpy_path:
        # Run the virtual display
        run_virtual_display_with_scrcpy(scrcpy_path)
    else:
        print("Failed to download scrcpy. Cannot continue.")
        sys.exit(1)
