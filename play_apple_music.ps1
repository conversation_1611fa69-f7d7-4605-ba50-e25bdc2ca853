# PowerShell script to play Apple Music on OnePlus phone via Bluetooth

Write-Host "Attempting to play Apple Music on your OnePlus 11R (CPH2487) at maximum volume..."
Write-Host "IMEI: 867268073936663"

# Check if the OnePlus device is paired
$btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }

if ($btDevices) {
    Write-Host "OnePlus device found in paired Bluetooth devices."
    
    # Try to use Windows 10's Bluetooth capabilities to send media control commands
    Write-Host "`nAttempting to control Apple Music via Bluetooth..."
    
    # Load required assemblies
    Add-Type -AssemblyName System.Windows.Forms
    
    # First, try to launch Apple Music by sending app launch command
    # This is a simulation as direct app launch via Bluetooth is limited
    Write-Host "Attempting to launch Apple Music app..."
    
    # Send media keys to potentially control phone's media playback
    Write-Host "Sending media control keys..."
    
    # First send play command to start music if it was previously playing
    [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
    Start-Sleep -Seconds 2
    
    # If that didn't work, try again
    [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
    Start-Sleep -Seconds 2
    
    # Send next track command to change to a potentially louder song
    Write-Host "Sending next track command..."
    [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_NEXT_TRACK}")
    Start-Sleep -Seconds 2
    
    # Send volume up commands multiple times to maximize volume
    Write-Host "Sending volume up commands to maximize volume..."
    for ($i = 0; $i -lt 20; $i++) {
        [System.Windows.Forms.SendKeys]::SendWait("{VOLUME_UP}")
        Start-Sleep -Milliseconds 200
    }
    
    Write-Host "Media commands sent. Check if your phone is playing Apple Music at maximum volume."
    
    # Additional options for controlling Apple Music
    Write-Host "`nAdditional options to try:"
    
    Write-Host "`n1. Use iTunes Remote Control on your computer"
    Write-Host "   - If you have iTunes installed, it might be able to control Apple Music on your phone"
    Write-Host "   - Open iTunes and look for your phone in the devices list"
    
    $openITunes = Read-Host "`nDo you have iTunes installed? Would you like to open it? (Y/N)"
    if ($openITunes -eq "Y" -or $openITunes -eq "y") {
        Start-Process "iTunes"
    }
    
    Write-Host "`n2. Try using Apple Music web player"
    Write-Host "   - If your Apple Music account is logged in on your computer"
    Write-Host "   - Playing music on your computer might trigger controls on your phone"
    
    $openAppleMusic = Read-Host "`nWould you like to open Apple Music web player? (Y/N)"
    if ($openAppleMusic -eq "Y" -or $openAppleMusic -eq "y") {
        Start-Process "https://music.apple.com/"
    }
    
    Write-Host "`n3. Try sending specific Bluetooth commands to trigger loud songs"
    Write-Host "   - Repeatedly sending play/pause and next track commands"
    Write-Host "   - This might eventually start a song with high volume"
    
    $sendMoreCommands = Read-Host "`nWould you like to send more media control commands? (Y/N)"
    if ($sendMoreCommands -eq "Y" -or $sendMoreCommands -eq "y") {
        Write-Host "Sending additional media commands..."
        
        # Send a series of commands to try different combinations
        for ($i = 0; $i -lt 5; $i++) {
            # Try play/pause
            [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
            Start-Sleep -Seconds 1
            
            # Try next track
            [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_NEXT_TRACK}")
            Start-Sleep -Seconds 2
            
            # Try play again
            [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
            Start-Sleep -Seconds 2
            
            # Max volume again
            for ($j = 0; $j -lt 5; $j++) {
                [System.Windows.Forms.SendKeys]::SendWait("{VOLUME_UP}")
                Start-Sleep -Milliseconds 200
            }
        }
        
        Write-Host "Additional commands sent. Check your phone for music playback."
    }
} else {
    Write-Host "OnePlus device not found in paired Bluetooth devices."
}

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
