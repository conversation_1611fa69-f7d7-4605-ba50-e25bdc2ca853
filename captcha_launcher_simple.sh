#!/data/data/com.termux/files/usr/bin/bash

# CAPTCHA Solver Launcher (Simple Version)
echo "CAPTCHA Solver Launcher (Simple Version)"
echo "========================================"
echo

# Change to home directory
cd $HOME

# Create bin directory if it doesn't exist
mkdir -p ~/bin

# Install required packages
echo "Installing required packages..."
pkg update -y
pkg install -y python

# Install only Pillow (no NumPy or other complex dependencies)
echo "Installing Python packages..."
pip install pillow

# Copy the CAPTCHA solver script
echo "Setting up CAPTCHA solver script..."
cp /sdcard/Download/captcha_solver_simple.py ~/bin/
chmod +x ~/bin/captcha_solver_simple.py

# Run the CAPTCHA solver
echo "Starting CAPTCHA solver..."
python ~/bin/captcha_solver_simple.py

# Keep terminal open after script finishes
echo "Press Enter to close"
read
