#!/data/data/com.termux/files/usr/bin/bash

# CAPTCHA Solver Launcher (Fully Automated Version)
echo "CAPTCHA Solver Launcher (Fully Automated Version)"
echo "================================================"
echo

# Change to home directory
cd $HOME

# Create bin directory if it doesn't exist
mkdir -p ~/bin

# Install required packages
echo "Installing required packages..."
pkg update -y
pkg install -y python android-tools tesseract

# Install only Pillow (no complex dependencies)
echo "Installing Python packages..."
pip install pillow

# Copy the CAPTCHA solver script
echo "Setting up CAPTCHA solver script..."
cp /sdcard/Download/auto_captcha_solver.py ~/bin/
chmod +x ~/bin/auto_captcha_solver.py

# Fix ADB authorization issues
echo "Fixing ADB authorization issues..."
adb kill-server
adb start-server

echo "Please check your phone for an authorization prompt and accept it."
echo "Press Enter when you've accepted the prompt..."
read

# Run the CAPTCHA solver
echo "Starting CAPTCHA solver..."
python ~/bin/auto_captcha_solver.py

# Keep terminal open after script finishes
echo "Press Enter to close"
read
