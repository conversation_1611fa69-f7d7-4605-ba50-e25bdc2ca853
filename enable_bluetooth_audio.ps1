# PowerShell script to enable Bluetooth audio on OnePlus phone

Write-Host "Attempting to enable Bluetooth audio on your OnePlus phone..."

# Check if the OnePlus device is paired
$btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }

if ($btDevices) {
    Write-Host "OnePlus device found in paired Bluetooth devices."
    
    # Instructions for enabling A2DP Sink mode
    Write-Host "`nTo enable your phone to receive audio from your computer, follow these steps on your phone:"
    Write-Host "1. Open Settings"
    Write-Host "2. Go to Bluetooth settings"
    Write-Host "3. Find your computer in the paired devices list"
    Write-Host "4. Tap the settings icon (gear) next to your computer's name"
    Write-Host "5. Enable 'Media Audio' option if available"
    Write-Host "6. If you don't see this option, try unpairing and repairing the devices"
    
    # Offer to open Bluetooth settings on Windows
    $openBtSettings = Read-Host "`nWould you like to open Bluetooth settings on your computer? (Y/N)"
    if ($openBtSettings -eq "Y" -or $openBtSettings -eq "y") {
        Start-Process "ms-settings:bluetooth"
    }
    
    Write-Host "`nAlternative method: Use your phone as a Bluetooth speaker"
    Write-Host "1. On your phone, download an app like 'Bluetooth Audio Receiver' from the Play Store"
    Write-Host "2. Open the app and follow instructions to set up your phone as a Bluetooth speaker"
    Write-Host "3. Your computer should then be able to connect to your phone as an audio output device"
    
    # Offer to open Play Store link
    $openPlayStore = Read-Host "`nWould you like to open a link to Bluetooth Audio Receiver apps? (Y/N)"
    if ($openPlayStore -eq "Y" -or $openPlayStore -eq "y") {
        Start-Process "https://play.google.com/store/search?q=bluetooth%20audio%20receiver&c=apps"
    }
} else {
    Write-Host "OnePlus device not found in paired Bluetooth devices."
    Write-Host "Please make sure your phone is paired with your computer via Bluetooth first."
}

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
