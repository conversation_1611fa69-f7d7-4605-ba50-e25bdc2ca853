@echo off
echo CAPTCHA Solver - Wireless Mode
echo ============================
echo.

REM Get the IP address of the phone
set /p IP_ADDRESS=Enter your phone's IP address (e.g., *************): 

REM Connect to the phone wirelessly
echo Connecting to phone at %IP_ADDRESS%:5555...
cd "D:\Android Platform tool\platform-tools"
.\adb connect %IP_ADDRESS%:5555

REM Check if connection was successful
.\adb devices | findstr "%IP_ADDRESS%" > nul
if %ERRORLEVEL% NEQ 0 (
    echo Failed to connect to phone. Please check your IP address and try again.
    pause
    exit /b 1
)

echo Successfully connected to phone!
echo.

:menu
echo What would you like to do?
echo 1. Start CAPTCHA app
echo 2. Enter text in CAPTCHA app
echo 3. Close CAPTCHA app
echo 4. Exit
echo.

set /p choice=Enter your choice (1-4): 

if "%choice%"=="1" (
    echo Starting CAPTCHA app...
    .\adb -s %IP_ADDRESS%:5555 shell "am start -n com.aadevelopers.captchaapp/.views.SplashActivity"
    echo CA<PERSON><PERSON>HA app started!
    echo.
    goto menu
)

if "%choice%"=="2" (
    set /p text=Enter text to input: 
    echo Tapping input field...
    .\adb -s %IP_ADDRESS%:5555 shell "input tap 620 1500"
    timeout /t 1 > nul
    echo Entering text: %text%
    .\adb -s %IP_ADDRESS%:5555 shell "input text %text%"
    timeout /t 1 > nul
    echo Tapping submit button...
    .\adb -s %IP_ADDRESS%:5555 shell "input tap 598 1732"
    echo Text submitted!
    echo.
    goto menu
)

if "%choice%"=="3" (
    echo Closing CAPTCHA app...
    .\adb -s %IP_ADDRESS%:5555 shell "am force-stop com.aadevelopers.captchaapp"
    echo CAPTCHA app closed!
    echo.
    goto menu
)

if "%choice%"=="4" (
    echo Disconnecting from phone...
    .\adb disconnect %IP_ADDRESS%:5555
    echo Goodbye!
    pause
    exit /b 0
)

echo Invalid choice. Please try again.
echo.
goto menu
