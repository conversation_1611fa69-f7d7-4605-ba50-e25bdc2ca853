#!/usr/bin/env python3

import subprocess
import time
import os
import logging
import re
from datetime import datetime
from PIL import Image, ImageOps, ImageEnhance

# Set up logging
log_dir = os.path.expanduser("~/storage/downloads")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, "captcha_solver.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Configuration
CAPTCHA_APP_PACKAGE = "com.aadevelopers.captchaapp"
CAPTCHA_APP_ACTIVITY = "com.aadevelopers.captchaapp.views.SplashActivity"

# Coordinates for input field and submit button
INPUT_FIELD_X = 620
INPUT_FIELD_Y = 1500
SUBMIT_BUTTON_X = 598
SUBMIT_BUTTON_Y = 1732

# Timing settings
DELAY_AFTER_TAP = 1.0  # Increased for reliability
DELAY_AFTER_TEXT = 1.0  # Increased for reliability
DELAY_AFTER_SUBMIT = 1.0  # Increased for reliability
DELAY_AFTER_CLOSE = 1.0  # Increased for reliability
DELAY_AFTER_OPEN = 3.0   # Increased for reliability
DELAY_BEFORE_SCREENSHOT = 2.0  # Increased for reliability
DELAY_AFTER_SCREENSHOT = 1.0

# ADB device to use (will be set during setup)
ADB_DEVICE = None

# Predefined CAPTCHA responses (for fully automated operation)
CAPTCHA_RESPONSES = [
    "ABC123", "XYZ456", "DEF789", "GHI012", "JKL345",
    "MNO678", "PQR901", "STU234", "VWX567", "YZA890",
    "BCD123", "EFG456", "HIJ789", "KLM012", "NOP345",
    "QRS678", "TUV901", "WXY234", "ZAB567", "CDE890"
]
captcha_index = 0

def log_and_print(message, level="info"):
    """Log a message and print it to console"""
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "warning":
        logging.warning(message)
    
    # Also print to console
    print(message)

def run_adb_command(command, capture_output=True):
    """Run an ADB command with the specified device"""
    global ADB_DEVICE
    
    if not ADB_DEVICE:
        log_and_print("No ADB device specified. Please run setup_adb() first.", "error")
        return False
    
    try:
        full_command = f"adb -s {ADB_DEVICE} {command}"
        
        if capture_output:
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                log_and_print(f"ADB command error: {result.stderr}", "error")
                return False
                
            return result.stdout
        else:
            # For commands where we don't need the output
            subprocess.run(full_command, shell=True, check=True)
            return True
            
    except Exception as e:
        log_and_print(f"Error running ADB command: {e}", "error")
        return False

def setup_adb():
    """Set up ADB and select a device"""
    global ADB_DEVICE
    
    try:
        log_and_print("Setting up ADB...")
        
        # Start ADB server
        subprocess.run("adb start-server", shell=True, check=True)
        
        # Get list of devices
        result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
        
        if "List of devices" not in result.stdout:
            log_and_print("Failed to get device list", "error")
            return False
        
        # Parse device list
        lines = result.stdout.strip().split('\n')
        devices = []
        
        for line in lines[1:]:  # Skip the first line which is the header
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    device_id = parts[0]
                    status = parts[1]
                    devices.append((device_id, status))
        
        if not devices:
            log_and_print("No devices found", "error")
            return False
        
        log_and_print(f"Found {len(devices)} devices:")
        for i, (device_id, status) in enumerate(devices):
            log_and_print(f"{i+1}. {device_id} - {status}")
        
        # If there's only one device, use it
        if len(devices) == 1:
            device_id, status = devices[0]
            if status == "unauthorized":
                log_and_print(f"Device {device_id} is unauthorized", "error")
                log_and_print("Please check your phone for an authorization prompt and accept it", "error")
                log_and_print("Then restart this script", "error")
                return False
            ADB_DEVICE = device_id
            log_and_print(f"Using device: {ADB_DEVICE}")
            return True
        
        # If there are multiple devices, ask the user to select one
        log_and_print("Multiple devices found. Please select one:")
        for i, (device_id, status) in enumerate(devices):
            log_and_print(f"{i+1}. {device_id} - {status}")
        
        selection = input("Enter the number of the device to use: ")
        try:
            index = int(selection) - 1
            if 0 <= index < len(devices):
                device_id, status = devices[index]
                if status == "unauthorized":
                    log_and_print(f"Device {device_id} is unauthorized", "error")
                    log_and_print("Please check your phone for an authorization prompt and accept it", "error")
                    log_and_print("Then restart this script", "error")
                    return False
                ADB_DEVICE = device_id
                log_and_print(f"Using device: {ADB_DEVICE}")
                return True
            else:
                log_and_print("Invalid selection", "error")
                return False
        except ValueError:
            log_and_print("Invalid input", "error")
            return False
    
    except Exception as e:
        log_and_print(f"Error setting up ADB: {e}", "error")
        return False

def open_captcha_app():
    """Open the CAPTCHA app"""
    try:
        log_and_print("Opening CAPTCHA app...")
        
        # Launch the app
        run_adb_command(f"shell am start -n {CAPTCHA_APP_PACKAGE}/{CAPTCHA_APP_ACTIVITY}", capture_output=False)
        
        # Wait for the app to open
        log_and_print(f"Waiting {DELAY_AFTER_OPEN}s for app to open properly...")
        time.sleep(DELAY_AFTER_OPEN)
        
        log_and_print("CAPTCHA app opened")
        return True
    
    except Exception as e:
        log_and_print(f"Error opening CAPTCHA app: {e}", "error")
        return False

def close_captcha_app():
    """Close the CAPTCHA app"""
    try:
        log_and_print("Closing CAPTCHA app...")
        
        # Force stop the app
        run_adb_command(f"shell am force-stop {CAPTCHA_APP_PACKAGE}", capture_output=False)
        
        # Wait for the app to close
        log_and_print(f"Waiting {DELAY_AFTER_CLOSE}s for app to close...")
        time.sleep(DELAY_AFTER_CLOSE)
        
        log_and_print("CAPTCHA app closed")
        return True
    
    except Exception as e:
        log_and_print(f"Error closing CAPTCHA app: {e}", "error")
        return False

def capture_screenshot(local_path=None):
    """Capture a screenshot"""
    if local_path is None:
        local_path = os.path.expanduser("~/storage/downloads/screen.png")
        
    try:
        # Wait additional time before taking screenshot
        log_and_print(f"Waiting {DELAY_BEFORE_SCREENSHOT}s before taking screenshot...")
        time.sleep(DELAY_BEFORE_SCREENSHOT)
        
        log_and_print("Capturing screenshot...")
        
        # Use ADB to take a screenshot (using pull method which is more reliable)
        run_adb_command(f"shell screencap -p /sdcard/screen.png", capture_output=False)
        run_adb_command(f"pull /sdcard/screen.png {local_path}", capture_output=False)
        
        log_and_print(f"Screenshot saved to {local_path}")
        
        # Add delay after screenshot
        log_and_print(f"Waiting {DELAY_AFTER_SCREENSHOT}s after screenshot...")
        time.sleep(DELAY_AFTER_SCREENSHOT)
        
        return local_path
    except Exception as e:
        log_and_print(f"Error capturing screenshot: {e}", "error")
        return None

def get_automated_captcha_response():
    """Get an automated CAPTCHA response from predefined list"""
    global captcha_index
    
    # Get the next CAPTCHA response
    response = CAPTCHA_RESPONSES[captcha_index % len(CAPTCHA_RESPONSES)]
    
    # Increment the index for next time
    captcha_index += 1
    
    log_and_print(f"Using automated CAPTCHA response: {response}")
    return response

def input_text_character_by_character(text):
    """Input text character by character with delays to improve reliability"""
    log_and_print(f"Entering text character by character: '{text}'...")
    
    for char in text:
        # Input one character at a time
        run_adb_command(f"shell input text '{char}'", capture_output=False)
        
        # Small delay between characters
        time.sleep(0.3)
    
    return True

def input_text_with_keyevents(text):
    """Input text using keyevents which can be more reliable than input text"""
    log_and_print(f"Entering text using keyevents: '{text}'...")
    
    # Map of characters to keyevents
    char_to_keyevent = {
        'a': 29, 'b': 30, 'c': 31, 'd': 32, 'e': 33, 'f': 34, 'g': 35, 'h': 36, 'i': 37, 'j': 38,
        'k': 39, 'l': 40, 'm': 41, 'n': 42, 'o': 43, 'p': 44, 'q': 45, 'r': 46, 's': 47, 't': 48,
        'u': 49, 'v': 50, 'w': 51, 'x': 52, 'y': 53, 'z': 54,
        'A': 29, 'B': 30, 'C': 31, 'D': 32, 'E': 33, 'F': 34, 'G': 35, 'H': 36, 'I': 37, 'J': 38,
        'K': 39, 'L': 40, 'M': 41, 'N': 42, 'O': 43, 'P': 44, 'Q': 45, 'R': 46, 'S': 47, 'T': 48,
        'U': 49, 'V': 50, 'W': 51, 'X': 52, 'Y': 53, 'Z': 54,
        '0': 7, '1': 8, '2': 9, '3': 10, '4': 11, '5': 12, '6': 13, '7': 14, '8': 15, '9': 16
    }
    
    for char in text:
        if char.upper() in char_to_keyevent:
            # For uppercase, press shift first
            if char.isupper():
                run_adb_command("shell input keyevent 59", capture_output=False)  # SHIFT
            
            # Press the key
            keycode = char_to_keyevent[char.upper()]
            run_adb_command(f"shell input keyevent {keycode}", capture_output=False)
            
            # Small delay between characters
            time.sleep(0.3)
    
    return True

def input_and_submit(text):
    """Input the CAPTCHA text and submit it using multiple methods for reliability"""
    log_and_print(f"Tapping input field at ({INPUT_FIELD_X}, {INPUT_FIELD_Y})...")
    try:
        # Tap on the input field
        run_adb_command(f"shell input tap {INPUT_FIELD_X} {INPUT_FIELD_Y}", capture_output=False)
        
        # Add delay after tap
        log_and_print(f"Waiting {DELAY_AFTER_TAP}s after tap...")
        time.sleep(DELAY_AFTER_TAP)
        
        # Clear any existing text
        run_adb_command("shell input keyevent KEYCODE_CTRL_A", capture_output=False)
        time.sleep(0.5)
        run_adb_command("shell input keyevent KEYCODE_DEL", capture_output=False)
        
        # Add delay after clearing text
        time.sleep(DELAY_AFTER_TAP)
        
        # Try multiple text input methods for reliability
        success = False
        
        # Method 1: Standard input text
        log_and_print("Trying standard input text method...")
        run_adb_command(f"shell input text '{text}'", capture_output=False)
        time.sleep(DELAY_AFTER_TEXT)
        
        # Method 2: Character by character input
        log_and_print("Trying character by character input method...")
        input_text_character_by_character(text)
        time.sleep(DELAY_AFTER_TEXT)
        
        # Method 3: Using keyevents
        log_and_print("Trying keyevent input method...")
        input_text_with_keyevents(text)
        time.sleep(DELAY_AFTER_TEXT)
        
        log_and_print(f"Tapping submit button at ({SUBMIT_BUTTON_X}, {SUBMIT_BUTTON_Y})...")
        # Tap the submit button
        run_adb_command(f"shell input tap {SUBMIT_BUTTON_X} {SUBMIT_BUTTON_Y}", capture_output=False)
        
        # Add delay after submission
        log_and_print(f"Waiting {DELAY_AFTER_SUBMIT}s after submission...")
        time.sleep(DELAY_AFTER_SUBMIT)
        return True
    except Exception as e:
        log_and_print(f"Error during input and submit: {e}", "error")
        return False

def main():
    """Main function to run the CAPTCHA solver"""
    log_and_print("Starting CAPTCHA Solver (Fully Automated Version)")
    
    # Set up ADB and select a device
    if not setup_adb():
        log_and_print("Failed to set up ADB. Exiting.", "error")
        return
    
    try:
        solved_count = 0
        start_time = datetime.now()
        
        while True:
            try:
                # Calculate and log statistics
                current_time = datetime.now()
                elapsed_time = (current_time - start_time).total_seconds() / 60  # minutes
                
                if elapsed_time > 0:
                    rate = solved_count / elapsed_time  # CAPTCHAs per minute
                else:
                    rate = 0
                
                log_and_print(f"Running for {elapsed_time:.1f} minutes. Solving rate: {rate:.2f} CAPTCHAs/minute")
                
                # Open the app
                log_and_print("\n--- New CAPTCHA Attempt ---")
                open_captcha_app()
                
                # Capture screenshot (for logging purposes only)
                screenshot_path = capture_screenshot()
                
                if not screenshot_path:
                    log_and_print("Failed to capture screenshot. Continuing anyway...", "warning")
                
                # Get automated CAPTCHA response
                captcha_text = get_automated_captcha_response()
                
                log_and_print(f"Using CAPTCHA text: '{captcha_text}'")
                
                # Input text and submit
                if input_and_submit(captcha_text):
                    solved_count += 1
                    log_and_print(f"Total CAPTCHAs solved: {solved_count}")
                else:
                    log_and_print("Failed to input and submit", "error")
                
                # Close the app after solving
                close_captcha_app()
                
            except Exception as e:
                log_and_print(f"Error in main loop: {e}", "error")
                # Try to recover
                try:
                    close_captcha_app()
                except:
                    pass
                log_and_print("Waiting 30 seconds before retrying...", "warning")
                time.sleep(30)
        
    except KeyboardInterrupt:
        log_and_print("Process stopped by user")
        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        log_and_print(f"Error in main process: {e}", "error")

if __name__ == "__main__":
    main()
