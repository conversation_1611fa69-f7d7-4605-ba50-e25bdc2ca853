
import cv2
import numpy as np
import time
import subprocess
import os
import re
from ppadb.client import Client
import easyocr  # Import EasyOCR instead of pytesseract
import torch  # Import torch to check GPU availability

# ADB path (using the platform-tools folder in the current directory)
ADB_PATH = "platform-tools\\adb.exe"

# Wireless connection settings
WIRELESS_MODE = False  # Default to USB for speed
DEVICE_IP = None  # Will be detected automatically
DEVICE_PORT = "5555"  # Default ADB port
DEVICE_SERIAL = None  # Will be set during connection

# App package and activity for Captcha Work
CAPTCHA_WORK_PACKAGE = "com.aadevelopers.captchaapp"  # Correct package name for Captcha Work

# Fixed coordinates for input field and submit button
INPUT_FIELD_X = 620
INPUT_FIELD_Y = 1500  # Exact position for the input field as provided
SUBMIT_BUTTON_X = 616
SUBMIT_BUTTON_Y = 1681  # Exact position for the submit button as provided

# Precise timing settings
DELAY_AFTER_TAP = 0.3      # 0.5s delay after tapping
DELAY_AFTER_TEXT = 0.3     # 0.5s delay after entering text
DELAY_AFTER_SUBMIT = 0.3   # 0.5s delay after submission
DELAY_AFTER_CLOSE = 0.1    # 0.5s delay after closing the app
DELAY_AFTER_OPEN = 2.0     # 2.0s delay after opening the app
DELAY_BEFORE_SCREENSHOT = 1.5  # 1.5s additional delay before taking screenshot
DELAY_AFTER_SCREENSHOT = 0.5  # 1.0s delay after taking screenshot before analysis

# Check if CUDA is available
cuda_available = torch.cuda.is_available()
if cuda_available:
    print(f"CUDA is available! Using GPU: {torch.cuda.get_device_name(0)}")
else:
    print("CUDA is not available. Using CPU instead.")

# Initialize EasyOCR reader with GPU if available
print("Initializing EasyOCR (this may take a moment)...")
reader = easyocr.Reader(['en'], gpu=cuda_available)
print("EasyOCR initialized successfully")

def setup_wireless_adb():
    """Set up wireless ADB connection"""
    global DEVICE_IP, DEVICE_SERIAL

    try:
        # Check if ADB is available
        result = subprocess.run([ADB_PATH, "version"], capture_output=True, text=True)
        if "Android Debug Bridge" not in result.stdout:
            print(f"ADB not found at {ADB_PATH}. Make sure ADB is installed.")
            return False

        # Check for connected devices
        result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
        if "device" not in result.stdout:
            print("No devices connected via USB. Please connect your device via USB first.")
            return False

        # Set up TCP/IP mode
        print("Setting up TCP/IP mode...")
        subprocess.run([ADB_PATH, "tcpip", "5555"])
        time.sleep(2)

        # Get the device's IP address
        print("Getting device IP address...")
        result = subprocess.run([ADB_PATH, "shell", "ip", "addr", "show", "wlan0"], capture_output=True, text=True)

        # Extract IP address using regex
        ip_match = re.search(r'inet\s+(\d+\.\d+\.\d+\.\d+)', result.stdout)

        if ip_match:
            device_ip = ip_match.group(1)
            print(f"Found device IP: {device_ip}")

            # Update the global variable
            DEVICE_IP = device_ip

            # Connect to the device wirelessly
            print(f"Connecting to {device_ip}:5555...")
            subprocess.run([ADB_PATH, "connect", f"{device_ip}:5555"])

            # Check if the connection was successful
            result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
            if f"{device_ip}:5555" in result.stdout:
                print(f"Successfully connected to {device_ip}:5555")
                print("You can now disconnect the USB cable and continue wirelessly.")
                print(f"Your device IP is: {device_ip}")

                # Set the device serial
                DEVICE_SERIAL = f"{device_ip}:5555"

                return True
            else:
                print(f"Failed to connect to {device_ip}:5555")
                return False
        else:
            print("Could not find device IP address. Make sure Wi-Fi is enabled on your device.")
            return False

    except Exception as e:
        print(f"Error setting up wireless ADB: {e}")
        return False

def disconnect_all_devices():
    """Disconnect all wireless devices"""
    try:
        # Get list of connected devices
        result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')

        # Skip the first line (header)
        for line in lines[1:]:
            if ':' in line:  # Only wireless devices have a colon in their name
                device_id = line.split('\t')[0].strip()
                print(f"Disconnecting {device_id}...")
                subprocess.run([ADB_PATH, "disconnect", device_id])

        return True
    except Exception as e:
        print(f"Error disconnecting devices: {e}")
        return False

def open_captcha_work():
    """Open the Captcha Work app"""
    try:
        print("Opening Captcha Work app...")

        # Launch the app
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "monkey", "-p", CAPTCHA_WORK_PACKAGE, "-c", "android.intent.category.LAUNCHER", "1"], capture_output=True, text=True)

        # Wait a full second for the app to open properly
        print(f"Waiting {DELAY_AFTER_OPEN}s for app to open properly...")
        time.sleep(DELAY_AFTER_OPEN)

        print("Captcha Work app opened")
        return True

    except Exception as e:
        print(f"Error opening Captcha Work: {e}")
        return False

def close_captcha_work():
    """Close the Captcha Work app"""
    try:
        print("Closing Captcha Work app...")

        # Force stop the app
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "am", "force-stop", CAPTCHA_WORK_PACKAGE], check=True)

        # Wait for the app to close
        print(f"Waiting {DELAY_AFTER_CLOSE}s for app to close...")
        time.sleep(DELAY_AFTER_CLOSE)

        print("Captcha Work app closed")
        return True

    except Exception as e:
        print(f"Error closing Captcha Work: {e}")
        return False

def capture_screenshot(device, local_path="screen.png"):
    """Capture a screenshot from the device"""
    try:
        # Wait additional time before taking screenshot
        print(f"Waiting {DELAY_BEFORE_SCREENSHOT}s before taking screenshot...")
        time.sleep(DELAY_BEFORE_SCREENSHOT)

        print("Capturing screenshot from device...")
        img_bytes = device.screencap()
        with open(local_path, "wb") as f:
            f.write(img_bytes)
        print(f"Screenshot saved to {local_path}")

        # Add delay after screenshot to ensure it's properly saved
        print(f"Waiting {DELAY_AFTER_SCREENSHOT}s after screenshot...")
        time.sleep(DELAY_AFTER_SCREENSHOT)

        return local_path
    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return None

def extract_captcha_text(image_path):
    """Extract CAPTCHA text from the image using EasyOCR"""
    # Load the image
    img = cv2.imread(image_path)

    if img is None:
        print(f"Error: Could not load image from {image_path}")
        return ""

    # Get image dimensions
    height, width, _ = img.shape
    print(f"Image dimensions: {width}x{height}")

    # EXACT DIMENSIONS APPROACH: Use the exact dimensions from the screenshot (606 x 336)
    # Calculate the center position to place our crop
    center_x = width // 2
    center_y = height // 3  # Position in the upper third of the screen

    # Use exact dimensions: 606 x 336
    crop_width = 606
    crop_height = 336

    # Calculate the top-left corner of our crop area
    crop_x = center_x - (crop_width // 2)
    crop_y = center_y - (crop_height // 2)

    # Make sure we don't go out of bounds
    crop_x = max(0, min(crop_x, width - crop_width))
    crop_y = max(0, min(crop_y, height - crop_height))

    print(f"Exact crop: x={crop_x}, y={crop_y}, width={crop_width}, height={crop_height}")

    # Crop using the exact dimensions
    captcha_area = img[crop_y:crop_y+crop_height, crop_x:crop_x+crop_width]
    cv2.imwrite("captcha_area.png", captcha_area)

    # Use this as our main crop for text detection
    center_crop = captcha_area

    # Use EasyOCR to detect text
    print("Running EasyOCR on the cropped image...")
    results = reader.readtext(center_crop)

    # Process the results
    if results:
        # EasyOCR returns a list of [bbox, text, confidence]
        # We'll take the text with the highest confidence
        results.sort(key=lambda x: x[2], reverse=True)  # Sort by confidence

        for bbox, text, confidence in results:
            # Clean the text (keep only alphanumeric characters)
            cleaned_text = ''.join(c for c in text if c.isalnum())

            print(f"Detected text: '{cleaned_text}' (confidence: {confidence:.2f})")

            # If we found text with at least 5 characters and at most 15, return it
            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                return cleaned_text

    # If EasyOCR didn't find any text, try a different approach
    # Convert to grayscale and invert colors (white text on black background)
    gray = cv2.cvtColor(center_crop, cv2.COLOR_BGR2GRAY)
    inverted = cv2.bitwise_not(gray)
    cv2.imwrite("inverted.png", inverted)

    # Try EasyOCR on the inverted image
    print("Running EasyOCR on the inverted image...")
    results = reader.readtext(inverted)

    if results:
        results.sort(key=lambda x: x[2], reverse=True)  # Sort by confidence

        for bbox, text, confidence in results:
            cleaned_text = ''.join(c for c in text if c.isalnum())

            print(f"Detected text (inverted): '{cleaned_text}' (confidence: {confidence:.2f})")

            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                return cleaned_text

    # If we didn't find the white box or couldn't extract text, try a different approach
    print("Trying alternative approach...")

    # Try to extract text from the error message
    # The error message is usually in the format "ysxbiifm doesn't match with aoaonsto"
    # We want to extract "ysxbiifm"

    # Use EasyOCR on the full image
    print("Running EasyOCR on the full image...")
    results = reader.readtext(img)

    if results:
        # Look for text that might be the CAPTCHA code
        for bbox, text, confidence in results:
            # Clean the text
            cleaned_text = ''.join(c for c in text if c.isalnum())

            # Check if it looks like a CAPTCHA code (5-15 alphanumeric characters)
            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                print(f"Possible CAPTCHA text: '{cleaned_text}' (confidence: {confidence:.2f})")

                # Check if it's followed by "doesn't match" in the results
                for i, (_, next_text, _) in enumerate(results):
                    if "doesn't match" in next_text.lower() and i > 0:
                        prev_bbox, prev_text, prev_conf = results[i-1]
                        prev_cleaned = ''.join(c for c in prev_text if c.isalnum())

                        if prev_cleaned and 5 <= len(prev_cleaned) <= 15:
                            print(f"Extracted from error message: '{prev_cleaned}'")
                            return prev_cleaned

                # If we didn't find an error message but found a CAPTCHA-like text, return it
                return cleaned_text

    # If we still couldn't extract text, return empty string
    return ""

def input_and_submit(text):
    """Input the CAPTCHA text and submit it using fixed coordinates"""
    print(f"Tapping input field at ({INPUT_FIELD_X}, {INPUT_FIELD_Y})...")
    # Tap on the input field
    try:
        # Use -s flag to specify the device
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "input", "tap",
                       str(INPUT_FIELD_X), str(INPUT_FIELD_Y)], check=True)

        # Add delay after tap
        print(f"Waiting {DELAY_AFTER_TAP}s after tap...")
        time.sleep(DELAY_AFTER_TAP)

        # Clear any existing text (optional)
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "input", "keyevent", "KEYCODE_CTRL_A"], check=True)
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "input", "keyevent", "KEYCODE_DEL"], check=True)

        # Add delay after clearing text
        time.sleep(DELAY_AFTER_TAP)

        print(f"Entering text: '{text}'...")
        # Input the text - strip any whitespace and ensure only alphanumeric characters
        # This prevents extra spaces and special characters that might cause issues
        cleaned_text = ''.join(c for c in text if c.isalnum())
        print(f"Cleaned text for input: '{cleaned_text}'")
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "input", "text", cleaned_text], check=True)

        # Add delay after entering text
        print(f"Waiting {DELAY_AFTER_TEXT}s after entering text...")
        time.sleep(DELAY_AFTER_TEXT)

        print(f"Tapping submit button at ({SUBMIT_BUTTON_X}, {SUBMIT_BUTTON_Y})...")
        # Tap the submit button
        subprocess.run([ADB_PATH, "-s", DEVICE_SERIAL, "shell", "input", "tap",
                       str(SUBMIT_BUTTON_X), str(SUBMIT_BUTTON_Y)], check=True)

        # Add delay after submission
        print(f"Waiting {DELAY_AFTER_SUBMIT}s after submission...")
        time.sleep(DELAY_AFTER_SUBMIT)
        return True
    except Exception as e:
        print(f"Error during input and submit: {e}")
        return False

def connect_to_device():
    """Connect to the Android device (wireless or USB)"""
    global DEVICE_SERIAL

    if WIRELESS_MODE and DEVICE_IP:
        # Try to connect wirelessly
        try:
            # Connect to the device
            print(f"Connecting to device at {DEVICE_IP}:{DEVICE_PORT}...")
            subprocess.run([ADB_PATH, "connect", f"{DEVICE_IP}:{DEVICE_PORT}"])

            # Check if the connection was successful
            result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
            if f"{DEVICE_IP}:{DEVICE_PORT}" in result.stdout:
                print(f"Connected to device at {DEVICE_IP}:{DEVICE_PORT}")

                # Set the device serial
                DEVICE_SERIAL = f"{DEVICE_IP}:{DEVICE_PORT}"

                # Create a client and get the device
                client = Client(host='127.0.0.1', port=5037)
                devices = client.devices()

                if devices:
                    for device in devices:
                        if DEVICE_IP in device.serial:
                            print(f"Using wireless device: {device.serial}")
                            return device

                print("Device found but could not be accessed through ppadb")
                return None
            else:
                print(f"Failed to connect to device at {DEVICE_IP}:{DEVICE_PORT}")
                return None
        except Exception as e:
            print(f"Error connecting wirelessly: {e}")
            return None
    else:
        # Connect via USB
        try:
            # Check if ADB is available
            print("Checking ADB...")
            result = subprocess.run([ADB_PATH, "version"], capture_output=True, text=True)
            print(f"ADB version: {result.stdout.strip()}")

            # Check for connected devices
            print("Checking for connected devices...")
            result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
            print(f"ADB devices: {result.stdout.strip()}")

            # Connect via USB
            print("Connecting to device via ppadb...")
            client = Client(host='127.0.0.1', port=5037)
            devices = client.devices()

            if len(devices) == 0:
                print("No devices connected via USB")
                return None

            device = devices[0]

            # Set the device serial
            DEVICE_SERIAL = device.serial

            print(f"Connected to device via USB: {device.serial}")
            return device
        except Exception as e:
            print(f"Error connecting to device: {e}")
            return None

def main():
    print(f"Using ADB at: {ADB_PATH}")

    # Disconnect all existing wireless devices first
    print("Disconnecting any existing wireless devices...")
    disconnect_all_devices()

    # Ask if user wants to set up wireless ADB
    setup_choice = ("y")


    if setup_choice == 'y':
        if setup_wireless_adb():
            print("Wireless ADB set up successfully.")
            # Set wireless mode to True
            global WIRELESS_MODE
            WIRELESS_MODE = True
        else:
            print("Failed to set up wireless ADB. Falling back to USB mode.")
            WIRELESS_MODE = False

    # Connect to the device
    device = connect_to_device()
    if not device:
        print("Please connect an Android device and try again")
        return

    try:
        solved_count = 0
        last_texts = []  # Track the last 3 texts to detect repetition

        while True:
            # Open the app
            print("\n--- New CAPTCHA Attempt ---")
            open_captcha_work()

            # Capture screenshot
            print("Capturing screenshot...")
            screenshot_path = capture_screenshot(device)

            if not screenshot_path:
                print("Failed to capture screenshot. Retrying...")
                close_captcha_work()
                continue

            # Extract CAPTCHA text
            print("Processing CAPTCHA...")
            captcha_text = extract_captcha_text(screenshot_path)

            if captcha_text and len(captcha_text) >= 5:
                print(f"Final CAPTCHA text: '{captcha_text}'")

                # Track the last 3 texts
                last_texts.append(captcha_text)
                if len(last_texts) > 3:
                    last_texts.pop(0)

                # Check if we've seen the same text 3 times in a row
                if len(last_texts) == 3 and all(text == last_texts[0] for text in last_texts):
                    print("WARNING: Same CAPTCHA text detected 3 times in a row!")
                    print("This might indicate a problem with the app or the CAPTCHA system.")
                    print("Continuing anyway...")

                # Input text and submit
                if input_and_submit(captcha_text):
                    solved_count += 1
                    print(f"Total CAPTCHAs solved: {solved_count}")
                else:
                    print("Failed to input and submit")
            else:
                print("Failed to detect valid CAPTCHA text")

            # Close the app after solving
            close_captcha_work()

    except KeyboardInterrupt:
        print("Process stopped by user")
        print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        print(f"Error in main loop: {e}")
        print(f"Total CAPTCHAs solved: {solved_count}")

if __name__ == "__main__":
    main()
