# PowerShell script to send notifications to phone via Bluetooth

Write-Host "Attempting to send notifications to your phone (this may trigger sounds)..."

# Check if the OnePlus device is paired
$btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }

if ($btDevices) {
    Write-Host "OnePlus device found in paired Bluetooth devices."
    
    # Try to use Windows 10's built-in notification system
    # This might trigger notification sounds on your phone
    
    # Load Windows.UI.Notifications namespace
    [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType = WindowsRuntime] | Out-Null
    [Windows.Data.Xml.Dom.XmlDocument, Windows.Data.Xml.Dom.XmlDocument, ContentType = WindowsRuntime] | Out-Null
    
    # Create a toast notification
    $AppId = "Microsoft.Windows.PowerShell"
    $template = [Windows.UI.Notifications.ToastTemplateType]::ToastText02
    $xml = [Windows.UI.Notifications.ToastNotificationManager]::GetTemplateContent($template)
    $text = $xml.GetElementsByTagName("text")
    $text[0].AppendChild($xml.CreateTextNode("EMERGENCY ALERT")) | Out-Null
    $text[1].AppendChild($xml.CreateTextNode("This is an emergency notification. Please respond immediately.")) | Out-Null
    
    # Show the notification
    $toast = [Windows.UI.Notifications.ToastNotificationManager]::CreateToastNotifier($AppId)
    $notification = [Windows.UI.Notifications.ToastNotification]::new($xml)
    $toast.Show($notification)
    
    Write-Host "Notification sent. This might trigger a sound on your phone."
    
    # Alternative approach: Try to trigger Find My Device
    Write-Host "`nAttempting to trigger Find My Device (if enabled on your phone)..."
    Start-Process "ms-settings:findmydevice"
    
    Write-Host "If Find My Device is enabled on your phone, you can use it to make your phone play a sound."
    Write-Host "1. Go to the Find My Device settings"
    Write-Host "2. Sign in with your Microsoft account (if prompted)"
    Write-Host "3. Select your phone from the list of devices"
    Write-Host "4. Click 'Ring' to make your phone play a sound"
} else {
    Write-Host "OnePlus device not found in paired Bluetooth devices."
}

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
