#!/usr/bin/env python3

import subprocess
import time
import os
import logging
import cv2
import numpy as np
import easyocr
from datetime import datetime

# Set up logging
logging.basicConfig(
    filename='linux_captcha_solver.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Configuration
ADB_PATH = "adb"  # ADB should be in PATH
CAPTCHA_WORK_PACKAGE = "com.captchawork"  # Package name of the CAPTCHA Work app
CAPTCHA_WORK_ACTIVITY = "com.captchawork.MainActivity"  # Main activity of the app

# Coordinates for input field and submit button (may need adjustment for emulator)
INPUT_FIELD_X = 620
INPUT_FIELD_Y = 1500
SUBMIT_BUTTON_X = 598
SUBMIT_BUTTON_Y = 1732

# Timing settings
DELAY_AFTER_TAP = 0.5
DELAY_AFTER_TEXT = 0.5
DELAY_AFTER_SUBMIT = 0.5
DELAY_AFTER_CLOSE = 0.5
DELAY_AFTER_OPEN = 2.0
DELAY_BEFORE_SCREENSHOT = 1.5
DELAY_AFTER_SCREENSHOT = 1.0

# Initialize EasyOCR
print("Initializing EasyOCR (this may take a moment)...")
reader = easyocr.Reader(['en'], gpu=False)
print("EasyOCR initialized successfully")

def log_and_print(message, level="info"):
    """Log a message and print it to console"""
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "warning":
        logging.warning(message)
    
    # Also print to console
    print(message)

def open_captcha_work():
    """Open the Captcha Work app"""
    try:
        log_and_print("Opening Captcha Work app...")
        
        # Launch the app
        subprocess.run([ADB_PATH, "shell", "am", "start", "-n", f"{CAPTCHA_WORK_PACKAGE}/{CAPTCHA_WORK_ACTIVITY}"], 
                      check=True, capture_output=True)
        
        # Wait for the app to open
        log_and_print(f"Waiting {DELAY_AFTER_OPEN}s for app to open properly...")
        time.sleep(DELAY_AFTER_OPEN)
        
        log_and_print("Captcha Work app opened")
        return True
    
    except Exception as e:
        log_and_print(f"Error opening Captcha Work: {e}", "error")
        return False

def close_captcha_work():
    """Close the Captcha Work app"""
    try:
        log_and_print("Closing Captcha Work app...")
        
        # Force stop the app
        subprocess.run([ADB_PATH, "shell", "am", "force-stop", CAPTCHA_WORK_PACKAGE], 
                      check=True, capture_output=True)
        
        # Wait for the app to close
        log_and_print(f"Waiting {DELAY_AFTER_CLOSE}s for app to close...")
        time.sleep(DELAY_AFTER_CLOSE)
        
        log_and_print("Captcha Work app closed")
        return True
    
    except Exception as e:
        log_and_print(f"Error closing Captcha Work: {e}", "error")
        return False

def capture_screenshot(local_path="screen.png"):
    """Capture a screenshot from the device"""
    try:
        # Wait additional time before taking screenshot
        log_and_print(f"Waiting {DELAY_BEFORE_SCREENSHOT}s before taking screenshot...")
        time.sleep(DELAY_BEFORE_SCREENSHOT)
        
        log_and_print("Capturing screenshot from device...")
        subprocess.run([ADB_PATH, "exec-out", "screencap", "-p", ">", local_path], 
                      check=True, shell=True)
        log_and_print(f"Screenshot saved to {local_path}")
        
        # Add delay after screenshot
        log_and_print(f"Waiting {DELAY_AFTER_SCREENSHOT}s after screenshot...")
        time.sleep(DELAY_AFTER_SCREENSHOT)
        
        return local_path
    except Exception as e:
        log_and_print(f"Error capturing screenshot: {e}", "error")
        return None

def extract_captcha_text(image_path):
    """Extract CAPTCHA text from the image using EasyOCR"""
    # Load the image
    img = cv2.imread(image_path)

    if img is None:
        log_and_print(f"Error: Could not load image from {image_path}", "error")
        return ""

    # Get image dimensions
    height, width, _ = img.shape
    log_and_print(f"Image dimensions: {width}x{height}")

    # EXACT DIMENSIONS APPROACH: Use the exact dimensions from the screenshot (606 x 336)
    # Calculate the center position to place our crop
    center_x = width // 2
    center_y = height // 3  # Position in the upper third of the screen
    
    # Use exact dimensions: 606 x 336
    crop_width = 606
    crop_height = 336
    
    # Calculate the top-left corner of our crop area
    crop_x = center_x - (crop_width // 2)
    crop_y = center_y - (crop_height // 2)
    
    # Make sure we don't go out of bounds
    crop_x = max(0, min(crop_x, width - crop_width))
    crop_y = max(0, min(crop_y, height - crop_height))
    
    log_and_print(f"Exact crop: x={crop_x}, y={crop_y}, width={crop_width}, height={crop_height}")
    
    # Crop using the exact dimensions
    captcha_area = img[crop_y:crop_y+crop_height, crop_x:crop_x+crop_width]
    cv2.imwrite("captcha_area.png", captcha_area)
    
    # Use this as our main crop for text detection
    center_crop = captcha_area

    # Use EasyOCR to detect text
    log_and_print("Running EasyOCR on the cropped image...")
    results = reader.readtext(center_crop)

    # Process the results
    if results:
        # EasyOCR returns a list of [bbox, text, confidence]
        # We'll take the text with the highest confidence
        results.sort(key=lambda x: x[2], reverse=True)  # Sort by confidence

        for bbox, text, confidence in results:
            # Clean the text (keep only alphanumeric characters)
            cleaned_text = ''.join(c for c in text if c.isalnum())

            log_and_print(f"Detected text: '{cleaned_text}' (confidence: {confidence:.2f})")

            # If we found text with at least 5 characters and at most 15, return it
            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                return cleaned_text

    # If EasyOCR didn't find any text, try a different approach
    # Convert to grayscale and invert colors (white text on black background)
    log_and_print("Running EasyOCR on the inverted image...")
    gray = cv2.cvtColor(center_crop, cv2.COLOR_BGR2GRAY)
    inverted = cv2.bitwise_not(gray)
    cv2.imwrite("inverted.png", inverted)

    # Try EasyOCR on the inverted image
    results = reader.readtext(inverted)

    if results:
        results.sort(key=lambda x: x[2], reverse=True)  # Sort by confidence

        for bbox, text, confidence in results:
            cleaned_text = ''.join(c for c in text if c.isalnum())

            log_and_print(f"Detected text (inverted): '{cleaned_text}' (confidence: {confidence:.2f})")

            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                return cleaned_text

    # If still no text found, try the full image as a last resort
    log_and_print("Trying alternative approach...")
    log_and_print("Running EasyOCR on the full image...")
    results = reader.readtext(img)
    
    if results:
        # Filter results to find potential CAPTCHA text
        potential_captchas = []
        for bbox, text, confidence in results:
            cleaned_text = ''.join(c for c in text if c.isalnum())
            if cleaned_text and 5 <= len(cleaned_text) <= 15:
                potential_captchas.append((cleaned_text, confidence))
        
        # Sort by confidence
        potential_captchas.sort(key=lambda x: x[1], reverse=True)
        
        if potential_captchas:
            best_text, confidence = potential_captchas[0]
            log_and_print(f"Possible CAPTCHA text: '{best_text}' (confidence: {confidence:.2f})")
            return best_text
    
    return ""

def input_and_submit(text):
    """Input the CAPTCHA text and submit it using fixed coordinates"""
    log_and_print(f"Tapping input field at ({INPUT_FIELD_X}, {INPUT_FIELD_Y})...")
    # Tap on the input field
    try:
        # Use -s flag to specify the device
        subprocess.run([ADB_PATH, "shell", "input", "tap", 
                       str(INPUT_FIELD_X), str(INPUT_FIELD_Y)], check=True)
        
        # Add delay after tap
        log_and_print(f"Waiting {DELAY_AFTER_TAP}s after tap...")
        time.sleep(DELAY_AFTER_TAP)
        
        # Clear any existing text (optional)
        subprocess.run([ADB_PATH, "shell", "input", "keyevent", "KEYCODE_CTRL_A"], check=True)
        subprocess.run([ADB_PATH, "shell", "input", "keyevent", "KEYCODE_DEL"], check=True)
        
        # Add delay after clearing text
        time.sleep(DELAY_AFTER_TAP)
        
        log_and_print(f"Entering text: '{text}'...")
        # Input the text - strip any whitespace and ensure only alphanumeric characters
        # This prevents extra spaces and special characters that might cause issues
        cleaned_text = ''.join(c for c in text if c.isalnum())
        log_and_print(f"Cleaned text for input: '{cleaned_text}'")
        subprocess.run([ADB_PATH, "shell", "input", "text", cleaned_text], check=True)
        
        # Add delay after entering text
        log_and_print(f"Waiting {DELAY_AFTER_TEXT}s after entering text...")
        time.sleep(DELAY_AFTER_TEXT)
        
        log_and_print(f"Tapping submit button at ({SUBMIT_BUTTON_X}, {SUBMIT_BUTTON_Y})...")
        # Tap the submit button
        subprocess.run([ADB_PATH, "shell", "input", "tap", 
                       str(SUBMIT_BUTTON_X), str(SUBMIT_BUTTON_Y)], check=True)
        
        # Add delay after submission
        log_and_print(f"Waiting {DELAY_AFTER_SUBMIT}s after submission...")
        time.sleep(DELAY_AFTER_SUBMIT)
        return True
    except Exception as e:
        log_and_print(f"Error during input and submit: {e}", "error")
        return False

def main():
    """Main function to run the CAPTCHA solver"""
    log_and_print("Starting Linux CAPTCHA Solver for Virtual Android")
    
    try:
        solved_count = 0
        last_texts = []  # Track the last 3 texts to detect repetition
        start_time = datetime.now()
        
        while True:
            try:
                # Calculate and log statistics
                current_time = datetime.now()
                elapsed_time = (current_time - start_time).total_seconds() / 60  # minutes
                
                if elapsed_time > 0:
                    rate = solved_count / elapsed_time  # CAPTCHAs per minute
                else:
                    rate = 0
                
                log_and_print(f"Running for {elapsed_time:.1f} minutes. Solving rate: {rate:.2f} CAPTCHAs/minute")
                
                # Open the app
                log_and_print("\n--- New CAPTCHA Attempt ---")
                open_captcha_work()
                
                # Capture screenshot
                screenshot_path = capture_screenshot()
                
                if not screenshot_path:
                    log_and_print("Failed to capture screenshot. Retrying...", "error")
                    close_captcha_work()
                    continue
                
                # Extract CAPTCHA text
                log_and_print("Processing CAPTCHA...")
                captcha_text = extract_captcha_text(screenshot_path)
                
                if captcha_text and len(captcha_text) >= 5:
                    log_and_print(f"Final CAPTCHA text: '{captcha_text}'")
                    
                    # Track the last 3 texts
                    last_texts.append(captcha_text)
                    if len(last_texts) > 3:
                        last_texts.pop(0)
                    
                    # Check if we've seen the same text 3 times in a row
                    if len(last_texts) == 3 and all(text == last_texts[0] for text in last_texts):
                        log_and_print("WARNING: Same CAPTCHA text detected 3 times in a row!", "warning")
                        log_and_print("This might indicate a problem with the app or the CAPTCHA system.", "warning")
                    
                    # Input text and submit
                    if input_and_submit(captcha_text):
                        solved_count += 1
                        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
                    else:
                        log_and_print("Failed to input and submit", "error")
                else:
                    log_and_print("Failed to detect valid CAPTCHA text", "error")
                
                # Close the app after solving
                close_captcha_work()
                
            except Exception as e:
                log_and_print(f"Error in main loop: {e}", "error")
                # Try to recover
                try:
                    close_captcha_work()
                except:
                    pass
                log_and_print("Waiting 30 seconds before retrying...", "warning")
                time.sleep(30)
        
    except KeyboardInterrupt:
        log_and_print("Process stopped by user")
        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        log_and_print(f"Error in main process: {e}", "error")

if __name__ == "__main__":
    main()
