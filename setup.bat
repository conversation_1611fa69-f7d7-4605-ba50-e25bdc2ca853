@echo off
echo CAPTCHA Solver Virtual Environment Setup
echo ======================================
echo.

echo Checking for Python...
python --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Python not found! Please install Python 3.7 or higher.
    echo Visit: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo Checking for pip...
pip --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo pip not found! Please make sure pip is installed with Python.
    pause
    exit /b 1
)

echo.
echo Installing required Python packages...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install required packages!
    pause
    exit /b 1
)

echo.
echo Checking for scrcpy...
scrcpy --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo scrcpy not found! Please install scrcpy.
    echo Visit: https://github.com/Genymobile/scrcpy/releases
    echo Or install with: choco install scrcpy
    pause
    exit /b 1
)

echo.
echo Checking for ADB...
adb version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo ADB not found! Please make sure Android Platform Tools are installed.
    echo Visit: https://developer.android.com/studio/releases/platform-tools
    pause
    exit /b 1
)

echo.
echo Checking for connected devices...
adb devices | findstr "device" > NUL
if %ERRORLEVEL% NEQ 0 (
    echo No Android devices found! Please connect your phone via USB.
    echo Make sure USB debugging is enabled on your phone.
    pause
    exit /b 1
)

echo.
echo All prerequisites are met!
echo.
echo You can now install and start the CAPTCHA Solver service:
echo.
echo   1. Run as Administrator: manage_captcha_service.bat install
echo   2. Then: manage_captcha_service.bat start
echo.
echo Or test without installing as a service:
echo.
echo   manage_captcha_service.bat test
echo.

pause
