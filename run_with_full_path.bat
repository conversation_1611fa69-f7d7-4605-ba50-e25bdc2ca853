@echo off
echo Running CAPTCHA Solver with Virtual Display using full path to scrcpy...
echo.

set SCRCPY_PATH=%LOCALAPPDATA%\scrcpy\scrcpy.exe

echo Using scrcpy at: %SCRCPY_PATH%
echo.

if not exist "%SCRCPY_PATH%" (
    echo ERROR: scrcpy not found at %SCRCPY_PATH%
    echo Please run install_scrcpy.bat first.
    pause
    exit /b 1
)

echo Setting SCRCPY_PATH environment variable...
set SCRCPY_PATH_ENV=%SCRCPY_PATH%

echo Starting CAPTCHA solver...
python -c "import os; os.environ['SCRCPY_PATH'] = r'%SCRCPY_PATH_ENV%'; exec(open('captcha_solver_virtual.py').read())"

pause
