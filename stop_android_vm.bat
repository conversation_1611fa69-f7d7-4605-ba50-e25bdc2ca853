@echo off
echo Stopping Android-x86 VM...

REM Check if VirtualBox is installed
if not exist "C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" (
    echo VirtualBox not found at the default location.
    pause
    exit /b 1
)

REM Disconnect ADB
echo Disconnecting ADB...
adb disconnect localhost:5555

REM Save the VM state
echo Saving VM state...
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" controlvm "Android-x86" savestate

echo VM stopped and state saved.
pause
