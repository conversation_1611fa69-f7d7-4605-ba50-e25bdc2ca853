#!/data/data/com.termux/files/usr/bin/bash

echo "Testing Termux script"
echo "====================="

# Install required packages
echo "Installing required packages..."
pkg update -y
pkg install -y python

echo "Creating a simple Python script..."
mkdir -p ~/bin

cat > ~/bin/hello.py << 'EOL'
#!/usr/bin/env python3

print("Hello from Python!")
print("This script is running in Termux!")

# Try to import some packages
try:
    import numpy
    print("NumPy is installed!")
except ImportError:
    print("NumPy is not installed.")

try:
    import cv2
    print("OpenCV is installed!")
except ImportError:
    print("OpenCV is not installed.")

try:
    import pytesseract
    print("Pytesseract is installed!")
except ImportError:
    print("Pytesseract is not installed.")

print("<PERSON>rip<PERSON> completed successfully!")
EOL

chmod +x ~/bin/hello.py

echo "Running the Python script..."
python ~/bin/hello.py

echo "Test completed!"
