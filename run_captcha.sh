#!/data/data/com.termux/files/usr/bin/bash

# This script will run the CAPTCHA solver using <PERSON><PERSON><PERSON>

# Make sure <PERSON><PERSON><PERSON> is running
echo "Checking if <PERSON><PERSON><PERSON> is running..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "echo test" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "<PERSON><PERSON><PERSON> is not running. Please start it first."
    exit 1
fi

echo "<PERSON><PERSON><PERSON> is running."

# Open the CAPTCHA Work app
echo "Opening CAPTCHA Work app..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "am start -n com.captchawork/.MainActivity"
sleep 2

# Tap the input field
echo "Tapping input field..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "input tap 620 1500"
sleep 0.5

# Enter text
echo "Entering text..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "input text TestCaptcha"
sleep 0.5

# Tap the submit button
echo "Tapping submit button..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "input tap 598 1732"
sleep 0.5

# Close the app
echo "Closing app..."
am startservice -n moe.shizuku.privileged.api/.service.ShizukuService --es command "am force-stop com.captchawork"

echo "Done!"
