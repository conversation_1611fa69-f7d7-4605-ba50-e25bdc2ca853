import subprocess
import time
import os
import sys

# Path to ADB - using the one from captcha_solver_rapid.py
from captcha_solver_rapid import ADB_PATH

def start_virtual_display():
    """Set up virtual display settings using ADB commands"""
    print("Setting up virtual display with ADB...")

    try:
        # First, make sure the device is connected
        result = subprocess.run([ADB_PATH, "devices"], capture_output=True, text=True)
        if "device" not in result.stdout:
            print("No device connected or device not authorized")
            return None

        # Force portrait mode
        print("Forcing portrait mode...")
        subprocess.run([ADB_PATH, "shell", "settings", "put", "system", "accelerometer_rotation", "0"],
                      check=True, capture_output=True)
        subprocess.run([ADB_PATH, "shell", "settings", "put", "system", "user_rotation", "0"],
                      check=True, capture_output=True)

        # Keep screen on while plugged in
        print("Setting screen to stay on while plugged in...")
        subprocess.run([ADB_PATH, "shell", "settings", "put", "global", "stay_on_while_plugged_in", "7"],
                      check=True, capture_output=True)

        # Create a dummy process object to maintain compatibility with the rest of the code
        class DummyProcess:
            def __init__(self):
                self.pid = 0
                self._returncode = None

            def poll(self):
                return self._returncode

            def terminate(self):
                pass

            def kill(self):
                pass

            def wait(self, timeout=None):
                pass

        # Try to turn off the screen using ADB directly
        print("Turning off screen using ADB...")
        try:
            subprocess.run([ADB_PATH, "shell", "input", "keyevent", "KEYCODE_POWER"],
                          check=False, capture_output=True)
        except Exception as e:
            print(f"Warning: Could not turn off screen: {e}")

        dummy_process = DummyProcess()
        print("Virtual display setup complete")
        return dummy_process

    except Exception as e:
        print(f"Error setting up virtual display: {e}")
        return None

def stop_virtual_display(process):
    """Clean up after virtual display"""
    print("Cleaning up virtual display settings...")
    try:
        # Reset screen timeout settings
        subprocess.run([ADB_PATH, "shell", "settings", "put", "global", "stay_on_while_plugged_in", "0"],
                      capture_output=True)
        print("Virtual display settings cleaned up")
    except Exception as e:
        print(f"Error cleaning up virtual display: {e}")

if __name__ == "__main__":
    try:
        process = start_virtual_display()
        if process:
            print("Virtual display is running. Press Ctrl+C to stop.")

            # Keep the script running
            while True:
                time.sleep(1)

                # Check if process is still running
                if process.poll() is not None:
                    print("Virtual display process ended unexpectedly")
                    break

    except KeyboardInterrupt:
        print("\nStopping virtual display...")
        stop_virtual_display(process)
        sys.exit(0)
