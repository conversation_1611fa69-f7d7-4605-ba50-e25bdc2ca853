@echo off
echo Running CAPTCHA Solver with Off-Screen Window...
echo This will create a tiny window positioned off-screen.
echo.

set SCRCPY_PATH=%LOCALAPPDATA%\scrcpy\scrcpy.exe
if not exist "%SCRCPY_PATH%" (
    echo scrcpy not found at %SCRCPY_PATH%
    echo Running direct download script first...
    python direct_download_scrcpy.py
    set SCRCPY_PATH=D:\Android Platform tool\scrcpy_temp\scrcpy-win64-v2.1.1\scrcpy.exe
)

echo Starting scrcpy with off-screen window...
start "" "%SCRCPY_PATH%" --window-title "Virtual Display" --window-x 9999 --window-y 9999 --window-width 1 --window-height 1 --stay-awake --turn-screen-off

echo Waiting for scrcpy to initialize...
timeout /t 5 > nul

echo Starting CAPTCHA solver...
python captcha_solver_rapid.py

echo Cleaning up...
taskkill /f /im scrcpy.exe > nul 2>&1

echo Done!
pause
