@echo off
echo Running Captcha Solver with Virtual Display (scrcpy)...
echo Input: (620,1500) Submit: (598,1732) Crop: 606x336
echo (2s open + 1.5s wait + 1s analyze + 0.5s actions)

REM Check if scrcpy is installed
where scrcpy >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo scrcpy not found! Installing...
    call install_scrcpy.bat
)

echo Starting virtual display CAPTCHA solver...
python captcha_solver_virtual.py
pause
