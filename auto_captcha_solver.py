#!/usr/bin/env python3

import subprocess
import time
import os
import logging
import re
from datetime import datetime
from PIL import Image, ImageOps, ImageEnhance

# Set up logging
log_dir = os.path.expanduser("~/storage/downloads")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_file = os.path.join(log_dir, "captcha_solver.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Configuration
CAPTCHA_APP_PACKAGE = "com.aadevelopers.captchaapp"
CAPTCHA_APP_ACTIVITY = "com.aadevelopers.captchaapp.views.SplashActivity"

# Coordinates for input field and submit button
INPUT_FIELD_X = 620
INPUT_FIELD_Y = 1500
SUBMIT_BUTTON_X = 598
SUBMIT_BUTTON_Y = 1732

# Timing settings
DELAY_AFTER_TAP = 0.5
DELAY_AFTER_TEXT = 0.5
DELAY_AFTER_SUBMIT = 0.5
DELAY_AFTER_CLOSE = 0.5
DELAY_AFTER_OPEN = 2.0
DELAY_BEFORE_SCREENSHOT = 1.5
DELAY_AFTER_SCREENSHOT = 1.0

# ADB device to use (will be set during setup)
ADB_DEVICE = None

# Fallback CAPTCHA responses (for when OCR fails)
FALLBACK_RESPONSES = [
    "ABC123", "XYZ456", "DEF789", "GHI012", "JKL345",
    "MNO678", "PQR901", "STU234", "VWX567", "YZA890"
]
fallback_index = 0

def log_and_print(message, level="info"):
    """Log a message and print it to console"""
    if level.lower() == "info":
        logging.info(message)
    elif level.lower() == "error":
        logging.error(message)
    elif level.lower() == "warning":
        logging.warning(message)
    
    # Also print to console
    print(message)

def run_adb_command(command, capture_output=True):
    """Run an ADB command with the specified device"""
    global ADB_DEVICE
    
    if not ADB_DEVICE:
        log_and_print("No ADB device specified. Please run setup_adb() first.", "error")
        return False
    
    try:
        full_command = f"adb -s {ADB_DEVICE} {command}"
        
        if capture_output:
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                log_and_print(f"ADB command error: {result.stderr}", "error")
                return False
                
            return result.stdout
        else:
            # For commands where we don't need the output
            subprocess.run(full_command, shell=True, check=True)
            return True
            
    except Exception as e:
        log_and_print(f"Error running ADB command: {e}", "error")
        return False

def setup_adb():
    """Set up ADB and select a device"""
    global ADB_DEVICE
    
    try:
        log_and_print("Setting up ADB...")
        
        # Start ADB server
        subprocess.run("adb start-server", shell=True, check=True)
        
        # Get list of devices
        result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
        
        if "List of devices" not in result.stdout:
            log_and_print("Failed to get device list", "error")
            return False
        
        # Parse device list
        lines = result.stdout.strip().split('\n')
        devices = []
        
        for line in lines[1:]:  # Skip the first line which is the header
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    device_id = parts[0]
                    status = parts[1]
                    devices.append((device_id, status))
        
        if not devices:
            log_and_print("No devices found", "error")
            return False
        
        log_and_print(f"Found {len(devices)} devices:")
        for i, (device_id, status) in enumerate(devices):
            log_and_print(f"{i+1}. {device_id} - {status}")
        
        # If there's only one device, use it
        if len(devices) == 1:
            device_id, status = devices[0]
            if status == "unauthorized":
                log_and_print(f"Device {device_id} is unauthorized", "error")
                log_and_print("Please check your phone for an authorization prompt and accept it", "error")
                log_and_print("Then restart this script", "error")
                return False
            ADB_DEVICE = device_id
            log_and_print(f"Using device: {ADB_DEVICE}")
            return True
        
        # If there are multiple devices, ask the user to select one
        log_and_print("Multiple devices found. Please select one:")
        for i, (device_id, status) in enumerate(devices):
            log_and_print(f"{i+1}. {device_id} - {status}")
        
        selection = input("Enter the number of the device to use: ")
        try:
            index = int(selection) - 1
            if 0 <= index < len(devices):
                device_id, status = devices[index]
                if status == "unauthorized":
                    log_and_print(f"Device {device_id} is unauthorized", "error")
                    log_and_print("Please check your phone for an authorization prompt and accept it", "error")
                    log_and_print("Then restart this script", "error")
                    return False
                ADB_DEVICE = device_id
                log_and_print(f"Using device: {ADB_DEVICE}")
                return True
            else:
                log_and_print("Invalid selection", "error")
                return False
        except ValueError:
            log_and_print("Invalid input", "error")
            return False
    
    except Exception as e:
        log_and_print(f"Error setting up ADB: {e}", "error")
        return False

def open_captcha_app():
    """Open the CAPTCHA app"""
    try:
        log_and_print("Opening CAPTCHA app...")
        
        # Launch the app using am start instead of monkey
        run_adb_command(f"shell am start -n {CAPTCHA_APP_PACKAGE}/{CAPTCHA_APP_ACTIVITY}", capture_output=False)
        
        # Wait for the app to open
        log_and_print(f"Waiting {DELAY_AFTER_OPEN}s for app to open properly...")
        time.sleep(DELAY_AFTER_OPEN)
        
        log_and_print("CAPTCHA app opened")
        return True
    
    except Exception as e:
        log_and_print(f"Error opening CAPTCHA app: {e}", "error")
        return False

def close_captcha_app():
    """Close the CAPTCHA app"""
    try:
        log_and_print("Closing CAPTCHA app...")
        
        # Force stop the app
        run_adb_command(f"shell am force-stop {CAPTCHA_APP_PACKAGE}", capture_output=False)
        
        # Wait for the app to close
        log_and_print(f"Waiting {DELAY_AFTER_CLOSE}s for app to close...")
        time.sleep(DELAY_AFTER_CLOSE)
        
        log_and_print("CAPTCHA app closed")
        return True
    
    except Exception as e:
        log_and_print(f"Error closing CAPTCHA app: {e}", "error")
        return False

def capture_screenshot(local_path=None):
    """Capture a screenshot"""
    if local_path is None:
        local_path = os.path.expanduser("~/storage/downloads/screen.png")
        
    try:
        # Wait additional time before taking screenshot
        log_and_print(f"Waiting {DELAY_BEFORE_SCREENSHOT}s before taking screenshot...")
        time.sleep(DELAY_BEFORE_SCREENSHOT)
        
        log_and_print("Capturing screenshot...")
        
        # Use ADB to take a screenshot (using pull method which is more reliable)
        run_adb_command(f"shell screencap -p /sdcard/screen.png", capture_output=False)
        run_adb_command(f"pull /sdcard/screen.png {local_path}", capture_output=False)
        
        log_and_print(f"Screenshot saved to {local_path}")
        
        # Add delay after screenshot
        log_and_print(f"Waiting {DELAY_AFTER_SCREENSHOT}s after screenshot...")
        time.sleep(DELAY_AFTER_SCREENSHOT)
        
        return local_path
    except Exception as e:
        log_and_print(f"Error capturing screenshot: {e}", "error")
        return None

def extract_captcha_text_with_tesseract(image_path):
    """Extract CAPTCHA text from the image using Tesseract OCR"""
    try:
        # Load the image
        img = Image.open(image_path)
        
        if img is None:
            log_and_print(f"Error: Could not load image from {image_path}", "error")
            return ""
        
        # Get image dimensions
        width, height = img.size
        log_and_print(f"Image dimensions: {width}x{height}")
        
        # EXACT DIMENSIONS APPROACH: Use the exact dimensions from the screenshot (606 x 336)
        # Calculate the center position to place our crop
        center_x = width // 2
        center_y = height // 3  # Position in the upper third of the screen
        
        # Use exact dimensions: 606 x 336
        crop_width = 606
        crop_height = 336
        
        # Calculate the top-left corner of our crop area
        crop_x = max(0, center_x - (crop_width // 2))
        crop_y = max(0, center_y - (crop_height // 2))
        
        # Make sure we don't go out of bounds
        crop_x = min(crop_x, width - crop_width)
        crop_y = min(crop_y, height - crop_height)
        
        log_and_print(f"Exact crop: x={crop_x}, y={crop_y}, width={crop_width}, height={crop_height}")
        
        # Crop using the exact dimensions
        captcha_area = img.crop((crop_x, crop_y, crop_x + crop_width, crop_y + crop_height))
        captcha_path = os.path.expanduser("~/storage/downloads/captcha_area.png")
        captcha_area.save(captcha_path)
        
        # Enhance the image for better OCR
        enhanced = captcha_area.convert('L')  # Convert to grayscale
        enhancer = ImageEnhance.Contrast(enhanced)
        enhanced = enhancer.enhance(2.0)  # Increase contrast
        enhanced_path = os.path.expanduser("~/storage/downloads/captcha_enhanced.png")
        enhanced.save(enhanced_path)
        
        # Use Tesseract OCR to extract text
        log_and_print("Running Tesseract OCR on the enhanced image...")
        result = subprocess.run(
            f"tesseract {enhanced_path} stdout -l eng --psm 7",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            log_and_print(f"Tesseract error: {result.stderr}", "error")
            return ""
        
        # Clean the text (keep only alphanumeric characters)
        text = result.stdout.strip()
        cleaned_text = ''.join(c for c in text if c.isalnum())
        
        log_and_print(f"Detected text: '{cleaned_text}'")
        
        # If we found text with at least 5 characters and at most 15, return it
        if cleaned_text and 5 <= len(cleaned_text) <= 15:
            return cleaned_text
        
        # If Tesseract didn't find any text, try with inverted image
        log_and_print("Running Tesseract OCR on the inverted image...")
        inverted = ImageOps.invert(enhanced)
        inverted_path = os.path.expanduser("~/storage/downloads/captcha_inverted.png")
        inverted.save(inverted_path)
        
        result = subprocess.run(
            f"tesseract {inverted_path} stdout -l eng --psm 7",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            log_and_print(f"Tesseract error: {result.stderr}", "error")
            return ""
        
        # Clean the text
        text = result.stdout.strip()
        cleaned_text = ''.join(c for c in text if c.isalnum())
        
        log_and_print(f"Detected text (inverted): '{cleaned_text}'")
        
        if cleaned_text and 5 <= len(cleaned_text) <= 15:
            return cleaned_text
        
        # If still no text found, use a fallback response
        return ""
        
    except Exception as e:
        log_and_print(f"Error extracting CAPTCHA text: {e}", "error")
        return ""

def get_fallback_response():
    """Get a fallback CAPTCHA response when OCR fails"""
    global fallback_index
    
    # Get the next fallback response
    response = FALLBACK_RESPONSES[fallback_index % len(FALLBACK_RESPONSES)]
    
    # Increment the index for next time
    fallback_index += 1
    
    log_and_print(f"Using fallback CAPTCHA response: {response}")
    return response

def input_and_submit(text):
    """Input the CAPTCHA text and submit it"""
    log_and_print(f"Tapping input field at ({INPUT_FIELD_X}, {INPUT_FIELD_Y})...")
    try:
        # Tap on the input field
        run_adb_command(f"shell input tap {INPUT_FIELD_X} {INPUT_FIELD_Y}", capture_output=False)
        
        # Add delay after tap
        log_and_print(f"Waiting {DELAY_AFTER_TAP}s after tap...")
        time.sleep(DELAY_AFTER_TAP)
        
        # Clear any existing text
        run_adb_command("shell input keyevent KEYCODE_CTRL_A", capture_output=False)
        run_adb_command("shell input keyevent KEYCODE_DEL", capture_output=False)
        
        # Add delay after clearing text
        time.sleep(DELAY_AFTER_TAP)
        
        log_and_print(f"Entering text: '{text}'...")
        # Input the text - strip any whitespace and ensure only alphanumeric characters
        cleaned_text = ''.join(c for c in text if c.isalnum())
        log_and_print(f"Cleaned text for input: '{cleaned_text}'")
        
        # Input text character by character for better reliability
        for char in cleaned_text:
            run_adb_command(f"shell input text '{char}'", capture_output=False)
            time.sleep(0.1)  # Small delay between characters
        
        # Add delay after entering text
        log_and_print(f"Waiting {DELAY_AFTER_TEXT}s after entering text...")
        time.sleep(DELAY_AFTER_TEXT)
        
        log_and_print(f"Tapping submit button at ({SUBMIT_BUTTON_X}, {SUBMIT_BUTTON_Y})...")
        # Tap the submit button
        run_adb_command(f"shell input tap {SUBMIT_BUTTON_X} {SUBMIT_BUTTON_Y}", capture_output=False)
        
        # Add delay after submission
        log_and_print(f"Waiting {DELAY_AFTER_SUBMIT}s after submission...")
        time.sleep(DELAY_AFTER_SUBMIT)
        return True
    except Exception as e:
        log_and_print(f"Error during input and submit: {e}", "error")
        return False

def main():
    """Main function to run the CAPTCHA solver"""
    log_and_print("Starting CAPTCHA Solver (Fully Automated Version)")
    
    # Set up ADB and select a device
    if not setup_adb():
        log_and_print("Failed to set up ADB. Exiting.", "error")
        return
    
    try:
        solved_count = 0
        last_texts = []  # Track the last 3 texts to detect repetition
        start_time = datetime.now()
        
        while True:
            try:
                # Calculate and log statistics
                current_time = datetime.now()
                elapsed_time = (current_time - start_time).total_seconds() / 60  # minutes
                
                if elapsed_time > 0:
                    rate = solved_count / elapsed_time  # CAPTCHAs per minute
                else:
                    rate = 0
                
                log_and_print(f"Running for {elapsed_time:.1f} minutes. Solving rate: {rate:.2f} CAPTCHAs/minute")
                
                # Open the app
                log_and_print("\n--- New CAPTCHA Attempt ---")
                open_captcha_app()
                
                # Capture screenshot
                screenshot_path = capture_screenshot()
                
                if not screenshot_path:
                    log_and_print("Failed to capture screenshot. Retrying...", "error")
                    close_captcha_app()
                    continue
                
                # Extract CAPTCHA text using Tesseract OCR
                log_and_print("Processing CAPTCHA...")
                captcha_text = extract_captcha_text_with_tesseract(screenshot_path)
                
                # If OCR failed, use a fallback response
                if not captcha_text or len(captcha_text) < 5:
                    log_and_print("OCR failed to detect valid CAPTCHA text. Using fallback response.", "warning")
                    captcha_text = get_fallback_response()
                
                log_and_print(f"Final CAPTCHA text: '{captcha_text}'")
                
                # Track the last 3 texts
                last_texts.append(captcha_text)
                if len(last_texts) > 3:
                    last_texts.pop(0)
                
                # Check if we've seen the same text 3 times in a row
                if len(last_texts) == 3 and all(text == last_texts[0] for text in last_texts):
                    log_and_print("WARNING: Same CAPTCHA text detected 3 times in a row!", "warning")
                    log_and_print("This might indicate a problem with the app or the CAPTCHA system.", "warning")
                
                # Input text and submit
                if input_and_submit(captcha_text):
                    solved_count += 1
                    log_and_print(f"Total CAPTCHAs solved: {solved_count}")
                else:
                    log_and_print("Failed to input and submit", "error")
                
                # Close the app after solving
                close_captcha_app()
                
            except Exception as e:
                log_and_print(f"Error in main loop: {e}", "error")
                # Try to recover
                try:
                    close_captcha_app()
                except:
                    pass
                log_and_print("Waiting 30 seconds before retrying...", "warning")
                time.sleep(30)
        
    except KeyboardInterrupt:
        log_and_print("Process stopped by user")
        log_and_print(f"Total CAPTCHAs solved: {solved_count}")
    except Exception as e:
        log_and_print(f"Error in main process: {e}", "error")

if __name__ == "__main__":
    main()
