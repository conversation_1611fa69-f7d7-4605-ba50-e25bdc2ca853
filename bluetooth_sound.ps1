# PowerShell script to send media control commands via Bluetooth

Write-Host "Attempting to play sounds on your phone via Bluetooth AVRCP..."

# Load required assemblies for Bluetooth communication
Add-Type -AssemblyName System.Runtime.WindowsRuntime
$asTaskGeneric = ([System.WindowsRuntimeSystemExtensions].GetMethods() | Where-Object { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1 -and $_.GetParameters()[0].ParameterType.Name -eq 'IAsyncOperation`1' })[0]

# Function to send media control commands
function Send-MediaCommand {
    param (
        [string]$command
    )
    
    try {
        # Get Bluetooth devices
        $btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }
        
        if ($btDevices) {
            Write-Host "OnePlus device found. Attempting to send $command command..."
            
            # This is where we would use Windows.Devices.Bluetooth API to send AVRCP commands
            # However, this requires more complex code that's beyond a simple script
            
            Write-Host "Command sent. Check your phone for audio playback."
        } else {
            Write-Host "OnePlus device not found in paired Bluetooth devices."
        }
    } catch {
        Write-Host "Error sending command: $_"
    }
}

# Try to send play command
Send-MediaCommand -command "Play"

# Alternative approach: Use built-in Windows media keys
Write-Host "`nAttempting to send media keys to your phone..."
Add-Type -AssemblyName System.Windows.Forms

# Send Play/Pause key
[System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
Write-Host "Sent Play/Pause command"

# Send Volume Up key multiple times
for ($i = 0; $i -lt 10; $i++) {
    [System.Windows.Forms.SendKeys]::SendWait("{VOLUME_UP}")
}
Write-Host "Sent Volume Up commands"

Write-Host "`nIf your phone has a default music player with songs, this might start playback."
Write-Host "Check if you hear any sound from your phone."

# Alternative approach using HID commands
Write-Host "`nAnother option: Install a remote control app on your phone"
Write-Host "1. On your phone, install an app like 'Bluetooth Remote Control'"
Write-Host "2. The app will let your computer control your phone remotely"
Write-Host "3. You can then send commands to play sounds or alarms"

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
