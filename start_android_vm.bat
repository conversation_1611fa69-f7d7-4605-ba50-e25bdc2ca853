@echo off
echo Starting Android-x86 VM headlessly...

REM Check if VirtualBox is installed
if not exist "C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" (
    echo VirtualBox not found at the default location.
    echo Please install VirtualBox from: https://www.virtualbox.org/wiki/Downloads
    pause
    exit /b 1
)

REM Check if the VM exists
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" list vms | findstr "Android-x86" > nul
if %ERRORLEVEL% NEQ 0 (
    echo Android-x86 VM not found.
    echo Please follow the setup guide to create the VM first.
    pause
    exit /b 1
)

REM Start the VM headlessly
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" startvm "Android-x86" --type headless

echo VM started. To stop it, run: stop_android_vm.bat
echo.
echo Waiting for VM to boot...
timeout /t 30 > nul

echo Setting up ADB connection...
adb connect localhost:5555

echo.
echo Android-x86 VM is now running headlessly.
echo You can run the CAPTCHA solver with: run_captcha_solver_vm.bat
echo.
