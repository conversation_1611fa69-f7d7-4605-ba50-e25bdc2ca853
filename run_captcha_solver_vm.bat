@echo off
echo Running CAPTCHA Solver with Android-x86 VM...

REM Check if the VM is running
"C:\Program Files\Oracle\VirtualBox\VBoxManage.exe" list runningvms | findstr "Android-x86" > nul
if %ERRORLEVEL% NEQ 0 (
    echo Android-x86 VM is not running.
    echo Please start it first with: start_android_vm.bat
    pause
    exit /b 1
)

REM Check ADB connection
adb devices | findstr "localhost:5555" > nul
if %ERRORLEVEL% NEQ 0 (
    echo Connecting to Android-x86 VM via ADB...
    adb connect localhost:5555
    timeout /t 2 > nul
    
    REM Check if connection was successful
    adb devices | findstr "localhost:5555" > nul
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to connect to Android-x86 VM via ADB.
        pause
        exit /b 1
    )
)

echo Running CAPTCHA solver...
python captcha_solver_vm.py

pause
