#!/bin/bash

# Linux Virtual Android Environment Setup Script
# This script sets up a headless Android emulator in Linux

echo "Setting up Virtual Android Environment in Linux"
echo "==============================================="

# Check for required tools
command -v apt-get >/dev/null 2>&1 || { echo "This script requires apt-get. Please use a Debian/Ubuntu based system."; exit 1; }

# Install required packages
echo "Installing required packages..."
sudo apt-get update
sudo apt-get install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils android-tools-adb python3 python3-pip openjdk-11-jdk

# Create a directory for Android SDK
mkdir -p ~/android-sdk
cd ~/android-sdk

# Download Android command line tools
echo "Downloading Android command line tools..."
wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
unzip commandlinetools-linux-8512546_latest.zip
mkdir -p cmdline-tools/latest
mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null
rmdir cmdline-tools/latest/cmdline-tools 2>/dev/null

# Set up environment variables
export ANDROID_SDK_ROOT=~/android-sdk
export PATH=$PATH:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/platform-tools

# Accept licenses
echo "Accepting Android SDK licenses..."
yes | sdkmanager --licenses

# Install required SDK components
echo "Installing Android SDK components..."
sdkmanager "platform-tools" "platforms;android-30" "system-images;android-30;google_apis;x86_64" "emulator"

# Create a virtual device
echo "Creating virtual Android device..."
avdmanager create avd -n CaptchaDevice -k "system-images;android-30;google_apis;x86_64" -d "pixel"

# Install Python dependencies
echo "Installing Python packages..."
pip3 install opencv-python easyocr numpy pillow pure-python-adb

# Create a script to run the emulator headlessly
cat > run_headless_emulator.sh << 'EOL'
#!/bin/bash

# Run the Android emulator headlessly
echo "Starting headless Android emulator..."
$ANDROID_SDK_ROOT/emulator/emulator -avd CaptchaDevice -no-window -no-audio -no-boot-anim &
EMULATOR_PID=$!

# Wait for the emulator to boot
echo "Waiting for emulator to boot..."
adb wait-for-device

# Install the CAPTCHA Work app
echo "Installing CAPTCHA Work app..."
# You'll need to provide the APK file for the CAPTCHA Work app
# adb install captcha_work.apk

# Run the CAPTCHA solver script
echo "Running CAPTCHA solver..."
python3 linux_captcha_solver.py

# Clean up
kill $EMULATOR_PID
EOL

chmod +x run_headless_emulator.sh

# Create a systemd service file
cat > captcha-solver.service << 'EOL'
[Unit]
Description=CAPTCHA Solver Virtual Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$HOME/android-sdk
ExecStart=$HOME/android-sdk/run_headless_emulator.sh
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOL

echo "Setup complete!"
echo "To run the headless Android emulator, execute: ./run_headless_emulator.sh"
echo "To install as a service, copy captcha-solver.service to /etc/systemd/system/ and run:"
echo "  sudo systemctl enable captcha-solver.service"
echo "  sudo systemctl start captcha-solver.service"
