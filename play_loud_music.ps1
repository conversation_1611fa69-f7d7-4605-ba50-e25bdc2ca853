# PowerShell script to attempt to play loud music on OnePlus phone

Write-Host "Attempting to play loud music on your OnePlus 11R (CPH2487)..."
Write-Host "IMEI: 867268073936663"

# Check if the OnePlus device is paired
$btDevices = Get-PnpDevice -Class Bluetooth | Where-Object { $_.FriendlyName -like "*OnePlus*" }

if ($btDevices) {
    Write-Host "OnePlus device found in paired Bluetooth devices."
    
    # Try to use Windows 10's Bluetooth capabilities to send commands
    Write-Host "`nAttempting to send media control commands..."
    
    # Load required assemblies
    Add-Type -AssemblyName System.Windows.Forms
    
    # Send media keys to potentially control phone's media playback
    Write-Host "Sending media control keys..."
    
    # First try to launch music app by sending play command
    [System.Windows.Forms.SendKeys]::SendWait("{MEDIA_PLAY_PAUSE}")
    Start-Sleep -Seconds 2
    
    # Send volume up commands multiple times to maximize volume
    Write-Host "Sending volume up commands..."
    for ($i = 0; $i -lt 15; $i++) {
        [System.Windows.Forms.SendKeys]::SendWait("{VOLUME_UP}")
        Start-Sleep -Milliseconds 300
    }
    
    Write-Host "Media commands sent. Check if your phone is playing music."
    
    # Try to use PowerShell's Bluetooth capabilities
    Write-Host "`nAttempting to use Bluetooth to launch music app..."
    
    # This would require more advanced Bluetooth libraries
    # Instead, we'll provide instructions for using third-party tools
    
    Write-Host "`nAlternative options to try:"
    
    Write-Host "`n1. Use Bluetooth Terminal HC-05 on your computer"
    Write-Host "   - Download: https://www.microsoft.com/store/productId/9WZDNCRDFST8"
    Write-Host "   - Connect to your phone"
    Write-Host "   - Send commands to launch music apps"
    
    $openStore = Read-Host "`nWould you like to open the Microsoft Store to download Bluetooth Terminal? (Y/N)"
    if ($openStore -eq "Y" -or $openStore -eq "y") {
        Start-Process "ms-windows-store://pdp/?productid=9WZDNCRDFST8"
    }
    
    Write-Host "`n2. Try Bluetooth Speaker Connector"
    Write-Host "   - Download: https://www.microsoft.com/store/productId/9N9WCLWDQS5J"
    Write-Host "   - This app helps connect to Bluetooth audio devices"
    Write-Host "   - It might help establish an audio connection to your phone"
    
    $openSpeakerApp = Read-Host "`nWould you like to open the Microsoft Store to download Bluetooth Speaker Connector? (Y/N)"
    if ($openSpeakerApp -eq "Y" -or $openSpeakerApp -eq "y") {
        Start-Process "ms-windows-store://pdp/?productid=9N9WCLWDQS5J"
    }
    
    Write-Host "`n3. Try using Android Debug Bridge (ADB) over Bluetooth"
    Write-Host "   - This requires setting up ADB over Bluetooth"
    Write-Host "   - You can then send commands to play music at max volume"
    
    $setupADB = Read-Host "`nWould you like to attempt to set up ADB over Bluetooth? (Y/N)"
    if ($setupADB -eq "Y" -or $setupADB -eq "y") {
        # This would launch a separate script to set up ADB over Bluetooth
        Write-Host "Launching ADB setup script..."
        # Placeholder for actual implementation
    }
} else {
    Write-Host "OnePlus device not found in paired Bluetooth devices."
}

Write-Host "`nPress any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
