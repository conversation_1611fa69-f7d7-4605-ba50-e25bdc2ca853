@echo off
echo Playing loud music on your OnePlus 11R...
echo.

echo Step 1: Attempting to send media control commands via Bluetooth...
echo This may start Apple Music on your phone if it was recently used.

REM Use the built-in Windows tools to send media keys
echo Sending Play command...
powershell -Command "(New-Object -ComObject WScript.Shell).SendKeys('^{PLAY_PAUSE}')"
timeout /t 2 /nobreak >nul

echo Sending Volume Up commands to maximize volume...
for /L %%i in (1,1,15) do (
    powershell -Command "(New-Object -ComObject WScript.Shell).SendKeys('^{VOLUME_UP}')"
    timeout /t 1 /nobreak >nul
)

echo.
echo Step 2: Trying to launch Apple Music directly...
echo.

REM Try to use ADB to launch Apple Music if the phone is connected
echo Checking if your phone is connected via ADB...
platform-tools\adb devices

echo Attempting to connect to your phone at *************...
platform-tools\adb connect *************:5555
timeout /t 2 /nobreak >nul

echo Trying to launch Apple Music and set volume to maximum...
platform-tools\adb shell am start -n com.apple.android.music/com.apple.android.music.onboarding.activities.SplashActivity
timeout /t 3 /nobreak >nul

echo Setting volume to maximum...
platform-tools\adb shell input keyevent 24 24 24 24 24 24 24 24 24 24 24 24 24 24 24
timeout /t 1 /nobreak >nul

echo Sending play command...
platform-tools\adb shell input keyevent 85
timeout /t 2 /nobreak >nul

echo.
echo Step 3: Alternative method - Using Windows Media Controls
echo.

echo Opening Windows Media Controls panel...
start ms-settings:apps-volume

echo Please use the Media Controls panel to control your phone's audio.
echo 1. Look for your OnePlus device in the list
echo 2. Use the play/pause and volume controls

echo.
echo If none of these methods work, try:
echo 1. Open Apple Music on your computer: https://music.apple.com/
echo 2. Sign in with the same account as on your phone
echo 3. Play music at maximum volume - this might sync to your phone

echo.
echo Press any key to exit...
pause >nul
