@echo off
setlocal

echo CAPTCHA Solver Virtual Service Manager
echo =====================================
echo.

if "%1"=="" goto :help

if "%1"=="install" goto :install
if "%1"=="remove" goto :remove
if "%1"=="start" goto :start
if "%1"=="stop" goto :stop
if "%1"=="restart" goto :restart
if "%1"=="status" goto :status
if "%1"=="test" goto :test

echo Unknown command: %1
goto :help

:install
echo Installing CAPTCHA Solver service...
python captcha_service.py install
echo.
echo Service installed. You can now start it with:
echo   manage_captcha_service.bat start
goto :eof

:remove
echo Removing CAPTCHA Solver service...
python captcha_service.py remove
goto :eof

:start
echo Starting CAPTCHA Solver service...
python captcha_service.py start
goto :eof

:stop
echo Stopping CAPTCHA Solver service...
python captcha_service.py stop
goto :eof

:restart
echo Restarting CAPTCHA Solver service...
python captcha_service.py restart
goto :eof

:status
echo Checking CAPTCHA Solver service status...
sc query CaptchaSolverVirtual
goto :eof

:test
echo Running CAPTCHA Solver in test mode (interactive)...
echo Press Ctrl+C to stop
python captcha_service.py test
goto :eof

:help
echo Usage: manage_captcha_service.bat [command]
echo.
echo Available commands:
echo   install  - Install the service
echo   remove   - Remove the service
echo   start    - Start the service
echo   stop     - Stop the service
echo   restart  - Restart the service
echo   status   - Check service status
echo   test     - Run in test mode (interactive)
echo.
echo Example:
echo   manage_captcha_service.bat install
echo   manage_captcha_service.bat start
goto :eof
